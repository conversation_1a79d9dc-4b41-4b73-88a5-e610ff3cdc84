import { Request, Response, NextFunction } from 'express';

export interface APIError extends Error {
  statusCode?: number;
  code?: string;
  isOperational?: boolean;
}

// Middleware de tratamento de erros global
export function errorHandler(
  err: APIError,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  // Log do erro
  console.error('❌ Erro capturado:', {
    message: err.message,
    code: err.code,
    statusCode: err.statusCode,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    path: req.path,
    method: req.method,
  });

  // Definir status code padrão
  const statusCode = err.statusCode || 500;
  const isProduction = process.env.NODE_ENV === 'production';

  // Resposta de erro
  res.status(statusCode).json({
    success: false,
    error: {
      message: err.message || 'Erro interno do servidor',
      code: err.code || 'INTERNAL_ERROR',
      ...(isProduction ? {} : { stack: err.stack }),
    },
    metadata: {
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method,
    }
  });
}

// Middleware para capturar 404
export function notFoundHandler(req: Request, res: Response): void {
  res.status(404).json({
    success: false,
    error: {
      message: 'Endpoint não encontrado',
      code: 'NOT_FOUND',
    },
    metadata: {
      timestamp: new Date().toISOString(),
      path: req.path,
      method: req.method,
    }
  });
}

// Classe personalizada de erro da API
export class CustomAPIError extends Error implements APIError {
  statusCode: number;
  code: string;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500, code: string = 'API_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Object.setPrototypeOf(this, CustomAPIError.prototype);
    Error.captureStackTrace(this, this.constructor);
  }
}

// Erros específicos da aplicação
export class ValidationError extends CustomAPIError {
  constructor(message: string) {
    super(message, 400, 'VALIDATION_ERROR');
  }
}

export class AuthenticationError extends CustomAPIError {
  constructor(message: string = 'Não autorizado') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends CustomAPIError {
  constructor(message: string = 'Acesso negado') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends CustomAPIError {
  constructor(message: string = 'Recurso não encontrado') {
    super(message, 404, 'NOT_FOUND');
  }
}

export class RateLimitError extends CustomAPIError {
  constructor(message: string = 'Limite de requisições excedido') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

export class DeepSeekError extends CustomAPIError {
  constructor(message: string, code: string = 'DEEPSEEK_ERROR') {
    super(message, 503, code);
  }
}

// Wrapper para funções assíncronas
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}