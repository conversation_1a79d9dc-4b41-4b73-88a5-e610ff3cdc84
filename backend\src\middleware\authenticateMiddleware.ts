/**
 * Middleware de autenticação real usando JWT
 * Substitui o sistema mock por validação real
 */

import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/authService';

// Estender interface Request para incluir user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number;
        name: string;
        email: string;
        cpf: string;
        role: string;
        secretaria: string;
        departmentId: number;
      };
    }
  }
}

/**
 * Middleware de autenticação JWT
 */
export async function authenticate(req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    // Extrair token do header Authorization
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: 'Token de acesso não fornecido'
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer '

    // Verificar token usando AuthService
    const user = await authService.verifyToken(token);

    if (!user) {
      res.status(401).json({
        success: false,
        error: 'Token inválido ou expirado'
      });
      return;
    }

    // Anexar dados do usuário à requisição
    req.user = user;
    
    next();
  } catch (error) {
    console.error('Erro na autenticação:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno no servidor'
    });
  }
}

/**
 * Middleware de autorização por role
 */
export function authorize(...allowedRoles: string[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Usuário não autenticado'
      });
      return;
    }

    if (!allowedRoles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Acesso negado para este role'
      });
      return;
    }

    next();
  };
}

/**
 * Middleware de autorização por secretaria
 */
export function authorizeSecretaria(requiredSecretaria?: string) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Usuário não autenticado'  
      });
      return;
    }

    // Se não especificou secretaria, aceitar qualquer uma
    if (!requiredSecretaria) {
      next();
      return;
    }

    // Verificar se usuário tem acesso à secretaria
    if (req.user.secretaria !== requiredSecretaria && req.user.role !== 'admin') {
      res.status(403).json({
        success: false,
        error: 'Acesso negado para esta secretaria'
      });
      return;
    }

    next();
  };
}