# Plano de Integração com Banco de Dados - Fase 9

## Visão Geral

Este documento detalha o plano de integração do sistema de chatbot com os bancos de dados reais da Prefeitura de Valparaíso de Goiás.

---

## 1. Bancos de Dados Disponíveis

### PostgreSQL (Principal)
```
Host: *************
Port: 5411
User: otto
Database: pv_valparaiso
```

**Uso planejado:**
- Dados de usuários e autenticação
- Estrutura organizacional (secretarias)
- Permissões e roles
- Cache persistente de longo prazo
- Configurações do sistema

### MongoDB (Logs e Conversas)
```
Host: *************
Port: 2711
User: mongodb
Database: pv_valparaiso
```

**Uso planejado:**
- Histórico completo de conversas
- Logs de custos detalhados
- Métricas e analytics
- Auditoria de sistema
- Dados não estruturados

---

## 2. Schemas PostgreSQL (Prisma)

### 2.1 Schema de Usuários

```prisma
model User {
  id              String    @id @default(uuid())
  cpf             String    @unique
  email           String    @unique
  passwordHash    String
  name            String
  role            Role      @default(CONSULTA)
  secretariaId    String
  secretaria      Department @relation(fields: [secretariaId], references: [id])
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  lastLoginAt     DateTime?
  
  conversations   Conversation[]
  rateLimits      RateLimit[]
  costLogs        CostLog[]
}

enum Role {
  ADMIN
  GESTOR
  OPERADOR
  CONSULTA
}
```

### 2.2 Schema de Departamentos

```prisma
model Department {
  id              String    @id @default(uuid())
  code            String    @unique
  name            String
  description     String?
  contactEmail    String?
  contactPhone    String?
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  users           User[]
  conversations   Conversation[]
}
```

### 2.3 Schema de Conversas

```prisma
model Conversation {
  id              String    @id @default(uuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  departmentId    String
  department      Department @relation(fields: [departmentId], references: [id])
  startedAt       DateTime  @default(now())
  endedAt         DateTime?
  messageCount    Int       @default(0)
  totalCost       Float     @default(0)
  
  // Referência ao MongoDB para detalhes
  mongoId         String?   @unique
}
```

### 2.4 Schema de Cache Persistente

```prisma
model CacheEntry {
  id              String    @id @default(uuid())
  key             String    @unique
  type            CacheType
  secretaria      String
  query           String
  response        String    @db.Text
  tokens          Json
  cost            Float
  hitCount        Int       @default(0)
  lastHitAt       DateTime?
  expiresAt       DateTime
  createdAt       DateTime  @default(now())
  
  @@index([key, type])
  @@index([secretaria])
  @@index([expiresAt])
}

enum CacheType {
  EXACT
  SEMANTIC
  CONTEXT
}
```

### 2.5 Schema de Rate Limiting

```prisma
model RateLimit {
  id              String    @id @default(uuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  date            DateTime  @db.Date
  messagesUsed    Int       @default(0)
  tokensUsed      Int       @default(0)
  
  @@unique([userId, date])
  @@index([date])
}
```

### 2.6 Schema de Logs de Custo

```prisma
model CostLog {
  id              String    @id @default(uuid())
  userId          String
  user            User      @relation(fields: [userId], references: [id])
  secretaria      String
  timestamp       DateTime  @default(now())
  promptTokens    Int
  completionTokens Int
  totalTokens     Int
  cost            Float
  withDiscount    Boolean
  cacheHit        Boolean
  
  @@index([timestamp])
  @@index([userId])
  @@index([secretaria])
}
```

---

## 3. Models MongoDB (Mongoose)

### 3.1 Model de Conversa Detalhada

```javascript
const ConversationSchema = new mongoose.Schema({
  postgresId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  departmentId: {
    type: String,
    required: true,
    index: true
  },
  messages: [{
    role: {
      type: String,
      enum: ['user', 'assistant', 'system'],
      required: true
    },
    content: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    tokens: {
      prompt: Number,
      completion: Number,
      total: Number
    },
    cost: {
      amount: Number,
      withDiscount: Boolean
    },
    cacheHit: {
      type: Boolean,
      default: false
    },
    cacheType: {
      type: String,
      enum: ['exact', 'semantic', 'none']
    },
    processingTime: Number,
    metadata: mongoose.Schema.Types.Mixed
  }],
  summary: {
    totalMessages: Number,
    totalCost: Number,
    totalTokens: Number,
    cacheHitRate: Number,
    avgResponseTime: Number
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Índices para performance
ConversationSchema.index({ 'messages.timestamp': -1 });
ConversationSchema.index({ createdAt: -1 });
```

### 3.2 Model de Log de Sistema

```javascript
const SystemLogSchema = new mongoose.Schema({
  level: {
    type: String,
    enum: ['debug', 'info', 'warn', 'error', 'critical'],
    required: true,
    index: true
  },
  category: {
    type: String,
    enum: ['auth', 'api', 'cache', 'cost', 'security', 'system'],
    required: true,
    index: true
  },
  action: {
    type: String,
    required: true,
    index: true
  },
  userId: {
    type: String,
    index: true
  },
  secretaria: {
    type: String,
    index: true
  },
  message: {
    type: String,
    required: true
  },
  details: mongoose.Schema.Types.Mixed,
  stack: String,
  ip: String,
  userAgent: String,
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  }
}, {
  capped: {
    size: 1073741824, // 1GB
    max: 1000000      // 1M documentos
  }
});
```

### 3.3 Model de Analytics

```javascript
const AnalyticsSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: ['hourly', 'daily', 'monthly'],
    required: true
  },
  metrics: {
    totalRequests: Number,
    uniqueUsers: Number,
    totalCost: Number,
    totalTokens: Number,
    cacheHits: Number,
    cacheMisses: Number,
    avgResponseTime: Number,
    errors: Number,
    
    bySecretaria: [{
      secretaria: String,
      requests: Number,
      users: Number,
      cost: Number,
      tokens: Number
    }],
    
    byHour: [{
      hour: Number,
      requests: Number,
      cost: Number,
      discountActive: Boolean
    }],
    
    topQueries: [{
      query: String,
      count: Number,
      avgCost: Number,
      avgResponseTime: Number
    }],
    
    costBreakdown: {
      cache: Number,
      discount: Number,
      regular: Number,
      total: Number,
      savings: Number
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Índice composto para queries eficientes
AnalyticsSchema.index({ date: -1, type: 1 });
```

---

## 4. Repository Pattern Implementation

### 4.1 User Repository

```typescript
// backend/src/repositories/userRepository.ts
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

export class UserRepository {
  constructor(private prisma: PrismaClient) {}

  async findByEmail(email: string) {
    return this.prisma.user.findUnique({
      where: { email },
      include: { secretaria: true }
    });
  }

  async findByCPF(cpf: string) {
    return this.prisma.user.findUnique({
      where: { cpf },
      include: { secretaria: true }
    });
  }

  async create(data: {
    cpf: string;
    email: string;
    password: string;
    name: string;
    role: string;
    secretariaId: string;
  }) {
    const passwordHash = await bcrypt.hash(data.password, 12);
    
    return this.prisma.user.create({
      data: {
        ...data,
        passwordHash,
        role: data.role as any
      },
      include: { secretaria: true }
    });
  }

  async updateLastLogin(id: string) {
    return this.prisma.user.update({
      where: { id },
      data: { lastLoginAt: new Date() }
    });
  }

  async validatePassword(user: any, password: string) {
    return bcrypt.compare(password, user.passwordHash);
  }
}
```

### 4.2 Conversation Repository

```typescript
// backend/src/repositories/conversationRepository.ts
import { PrismaClient } from '@prisma/client';
import { ConversationModel } from '../models/conversation';

export class ConversationRepository {
  constructor(
    private prisma: PrismaClient,
    private mongodb: typeof ConversationModel
  ) {}

  async create(userId: string, departmentId: string) {
    // Criar no PostgreSQL
    const pgConversation = await this.prisma.conversation.create({
      data: {
        userId,
        departmentId
      }
    });

    // Criar no MongoDB
    const mongoConversation = await this.mongodb.create({
      postgresId: pgConversation.id,
      userId,
      departmentId,
      messages: []
    });

    // Atualizar referência
    await this.prisma.conversation.update({
      where: { id: pgConversation.id },
      data: { mongoId: mongoConversation._id.toString() }
    });

    return { pgConversation, mongoConversation };
  }

  async addMessage(conversationId: string, message: any) {
    // Atualizar MongoDB
    const updated = await this.mongodb.findOneAndUpdate(
      { postgresId: conversationId },
      {
        $push: { messages: message },
        $inc: { 
          'summary.totalMessages': 1,
          'summary.totalCost': message.cost?.amount || 0,
          'summary.totalTokens': message.tokens?.total || 0
        }
      },
      { new: true }
    );

    // Atualizar PostgreSQL
    await this.prisma.conversation.update({
      where: { id: conversationId },
      data: {
        messageCount: { increment: 1 },
        totalCost: { increment: message.cost?.amount || 0 }
      }
    });

    return updated;
  }

  async getHistory(userId: string, limit: number = 10) {
    const conversations = await this.prisma.conversation.findMany({
      where: { userId },
      orderBy: { startedAt: 'desc' },
      take: limit,
      include: { department: true }
    });

    // Buscar detalhes do MongoDB para cada conversa
    const detailed = await Promise.all(
      conversations.map(async (conv) => {
        if (conv.mongoId) {
          const mongo = await this.mongodb.findById(conv.mongoId);
          return { ...conv, messages: mongo?.messages || [] };
        }
        return conv;
      })
    );

    return detailed;
  }
}
```

### 4.3 Cache Repository

```typescript
// backend/src/repositories/cacheRepository.ts
import { PrismaClient } from '@prisma/client';
import { redisCache } from '../config/redis';

export class CacheRepository {
  constructor(private prisma: PrismaClient) {}

  async get(key: string, type: string) {
    // Tentar Redis primeiro
    const redisValue = await redisCache.get(key);
    if (redisValue) {
      // Atualizar hit count no PostgreSQL
      await this.prisma.cacheEntry.updateMany({
        where: { key, type: type as any },
        data: { 
          hitCount: { increment: 1 },
          lastHitAt: new Date()
        }
      });
      return JSON.parse(redisValue);
    }

    // Fallback para PostgreSQL
    const pgCache = await this.prisma.cacheEntry.findFirst({
      where: {
        key,
        type: type as any,
        expiresAt: { gt: new Date() }
      }
    });

    if (pgCache) {
      // Re-popular no Redis
      await redisCache.setex(
        key,
        Math.floor((pgCache.expiresAt.getTime() - Date.now()) / 1000),
        pgCache.response
      );

      // Atualizar hit count
      await this.prisma.cacheEntry.update({
        where: { id: pgCache.id },
        data: { 
          hitCount: { increment: 1 },
          lastHitAt: new Date()
        }
      });

      return JSON.parse(pgCache.response);
    }

    return null;
  }

  async set(
    key: string,
    type: string,
    value: any,
    ttl: number,
    metadata: any
  ) {
    const stringValue = JSON.stringify(value);
    const expiresAt = new Date(Date.now() + ttl * 1000);

    // Salvar no Redis
    await redisCache.setex(key, ttl, stringValue);

    // Salvar no PostgreSQL
    await this.prisma.cacheEntry.upsert({
      where: { key },
      create: {
        key,
        type: type as any,
        secretaria: metadata.secretaria,
        query: metadata.query,
        response: stringValue,
        tokens: metadata.tokens,
        cost: metadata.cost,
        expiresAt
      },
      update: {
        response: stringValue,
        tokens: metadata.tokens,
        cost: metadata.cost,
        expiresAt,
        hitCount: 0
      }
    });
  }

  async cleanExpired() {
    const deleted = await this.prisma.cacheEntry.deleteMany({
      where: {
        expiresAt: { lt: new Date() }
      }
    });

    return deleted.count;
  }
}
```

---

## 5. Migração de Dados

### 5.1 Seed de Departamentos

```typescript
// backend/prisma/seed.ts
const departments = [
  { code: 'administracao', name: 'Secretaria de Administração' },
  { code: 'financas', name: 'Secretaria de Finanças' },
  { code: 'saude', name: 'Secretaria de Saúde' },
  { code: 'educacao', name: 'Secretaria de Educação' },
  { code: 'obras', name: 'Secretaria de Obras e Urbanismo' },
  { code: 'assistencia_social', name: 'Secretaria de Assistência Social' },
  { code: 'meio_ambiente', name: 'Secretaria de Meio Ambiente' }
];

async function seed() {
  for (const dept of departments) {
    await prisma.department.upsert({
      where: { code: dept.code },
      update: {},
      create: dept
    });
  }
}
```

### 5.2 Usuários Iniciais

```typescript
// Criar usuário admin padrão
await prisma.user.create({
  data: {
    cpf: '00000000000',
    email: '<EMAIL>',
    passwordHash: await bcrypt.hash('AlterarNoPrimeiroAcesso', 12),
    name: 'Administrador do Sistema',
    role: 'ADMIN',
    secretaria: {
      connect: { code: 'administracao' }
    }
  }
});
```

---

## 6. Configuração de Conexão

### 6.1 Arquivo .env Atualizado

```bash
# PostgreSQL
DATABASE_URL="postgresql://otto:[PASSWORD]@*************:5411/pv_valparaiso?schema=public"

# MongoDB
MONGODB_URI="mongodb://mongodb:[PASSWORD]@*************:2711/pv_valparaiso"

# Redis (já configurado)
REDIS_URL="redis://localhost:6379"
```

### 6.2 Configuração Prisma

```bash
# Instalar Prisma
cd backend
npm install prisma @prisma/client

# Inicializar Prisma
npx prisma init

# Gerar cliente
npx prisma generate

# Criar migração inicial
npx prisma migrate dev --name init

# Executar seed
npx prisma db seed
```

### 6.3 Configuração Mongoose

```typescript
// backend/src/config/mongodb.ts
import mongoose from 'mongoose';

export async function connectMongoDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI!);
    console.log('MongoDB conectado com sucesso');
  } catch (error) {
    console.error('Erro ao conectar MongoDB:', error);
    process.exit(1);
  }
}
```

---

## 7. Modificações no Sistema Atual

### 7.1 Autenticação JWT Real

```typescript
// backend/src/services/authService.ts
import jwt from 'jsonwebtoken';
import { UserRepository } from '../repositories/userRepository';

export class AuthService {
  constructor(private userRepo: UserRepository) {}

  async login(credentials: {
    login: string; // email ou CPF
    password: string;
    secretaria: string;
  }) {
    // Verificar se é email ou CPF
    const user = credentials.login.includes('@')
      ? await this.userRepo.findByEmail(credentials.login)
      : await this.userRepo.findByCPF(credentials.login);

    if (!user || user.secretaria.code !== credentials.secretaria) {
      throw new Error('Credenciais inválidas');
    }

    const validPassword = await this.userRepo.validatePassword(
      user,
      credentials.password
    );

    if (!validPassword) {
      throw new Error('Credenciais inválidas');
    }

    // Gerar JWT
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        secretaria: user.secretaria.code
      },
      process.env.JWT_SECRET!,
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Atualizar último login
    await this.userRepo.updateLastLogin(user.id);

    return { token, user };
  }
}
```

### 7.2 Middleware de Autenticação

```typescript
// backend/src/middleware/authenticate.ts
import jwt from 'jsonwebtoken';

export async function authenticate(req: any, res: any, next: any) {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Token não fornecido' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Token inválido' });
  }
}
```

### 7.3 Integração no Chat Controller

```typescript
// Modificar chatController para usar dados reais
const conversationRepo = new ConversationRepository(prisma, ConversationModel);

// Criar ou recuperar conversa
let conversation = await conversationRepo.getActive(
  req.user.id,
  req.user.secretaria
);

if (!conversation) {
  conversation = await conversationRepo.create(
    req.user.id,
    req.user.secretaria
  );
}

// Adicionar mensagem à conversa
await conversationRepo.addMessage(conversation.id, {
  role: 'user',
  content: message,
  timestamp: new Date()
});

// Processar com DeepSeek...

// Salvar resposta
await conversationRepo.addMessage(conversation.id, {
  role: 'assistant',
  content: response,
  timestamp: new Date(),
  tokens: deepSeekResponse.tokens,
  cost: deepSeekResponse.cost,
  cacheHit: false
});
```

---

## 8. Testes de Integração

### 8.1 Script de Teste de Conexão

```javascript
// test-database-connection.js
const { PrismaClient } = require('@prisma/client');
const mongoose = require('mongoose');

async function testConnections() {
  console.log('Testando conexões com bancos de dados...\n');

  // Testar PostgreSQL
  try {
    const prisma = new PrismaClient();
    await prisma.$connect();
    const count = await prisma.user.count();
    console.log(`✅ PostgreSQL conectado - ${count} usuários encontrados`);
    await prisma.$disconnect();
  } catch (error) {
    console.log('❌ PostgreSQL erro:', error.message);
  }

  // Testar MongoDB
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB conectado');
    await mongoose.disconnect();
  } catch (error) {
    console.log('❌ MongoDB erro:', error.message);
  }
}

testConnections();
```

### 8.2 Script de Teste de Autenticação

```javascript
// test-auth-flow.js
async function testAuthFlow() {
  // 1. Login
  const loginResponse = await axios.post('/api/auth/login', {
    login: '<EMAIL>',
    password: 'AlterarNoPrimeiroAcesso',
    secretaria: 'administracao'
  });

  const token = loginResponse.data.token;
  console.log('✅ Login realizado com JWT real');

  // 2. Fazer chamada autenticada
  const chatResponse = await axios.post(
    '/api/chat/message',
    {
      message: 'Teste com banco de dados real',
      secretaria: 'administracao'
    },
    {
      headers: { Authorization: `Bearer ${token}` }
    }
  );

  console.log('✅ Mensagem processada e salva no banco');
}
```

---

## 9. Cronograma de Implementação

### Semana 1 - Setup e Schemas
- [ ] Configurar conexões com bancos
- [ ] Criar schemas Prisma
- [ ] Criar models Mongoose
- [ ] Executar migrações
- [ ] Popular dados iniciais

### Semana 2 - Repositories e Services
- [ ] Implementar repositories
- [ ] Atualizar services para usar repos
- [ ] Sistema de autenticação JWT
- [ ] Testes de integração

### Semana 3 - Ajustes e Otimização
- [ ] Migrar cache para persistente
- [ ] Otimizar queries
- [ ] Logs estruturados
- [ ] Documentação final

---

## 10. Considerações de Segurança

### Conexões Seguras
- Usar SSL/TLS para conexões remotas
- Rotacionar credenciais regularmente
- Implementar connection pooling
- Monitorar tentativas de acesso

### Backup e Recovery
- Backup diário do PostgreSQL
- Replicação do MongoDB
- Snapshots do Redis
- Plano de disaster recovery

### Auditoria
- Log de todas as operações sensíveis
- Rastreamento de alterações
- Compliance com LGPD
- Relatórios de acesso

---

*Documento criado em: 23/01/2025*  
*Próxima revisão: Após implementação da Fase 9*