/**
 * Script para extrair conhecimento municipal do PostgreSQL
 * para melhorar os prompts da DeepSeek API
 */

import { Client } from 'pg';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

interface MunicipalKnowledge {
  departamentos: any[];
  usuarios: any[];
  estatisticas: any;
  estruturaOrganizacional: any;
  conhecimentoTextual: any[];
  padroes: any;
}

async function extractMunicipalKnowledge(): Promise<MunicipalKnowledge> {
  console.log(chalk.blue('\n=== EXTRAÇÃO DE CONHECIMENTO MUNICIPAL ===\n'));
  
  const client = new Client(postgresConfig);
  const knowledge: MunicipalKnowledge = {
    departamentos: [],
    usuarios: [],
    estatisticas: {},
    estruturaOrganizacional: {},
    conhecimentoTextual: [],
    padroes: {}
  };

  try {
    await client.connect();
    console.log(chalk.green('✅ Conectado ao PostgreSQL'));

    // 1. LISTAR TODAS AS TABELAS
    console.log(chalk.yellow('\n📋 Descobrindo todas as tabelas disponíveis:'));
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);

    const tables = tablesResult.rows.map(row => row.table_name);
    console.log(chalk.cyan(`Encontradas ${tables.length} tabelas:`));
    tables.forEach((table, index) => {
      console.log(`  ${index + 1}. ${table}`);
    });

    // 2. ANALISAR DEPARTAMENTOS DETALHADAMENTE
    console.log(chalk.yellow('\n🏢 Analisando departamentos:'));
    const departamentosResult = await client.query(`
      SELECT id, descricao, endereco, horario_atendimento, email, 
             slogan, ativo, tipo, icone
      FROM departamentos 
      WHERE ativo = true 
      ORDER BY descricao
      LIMIT 50
    `);

    knowledge.departamentos = departamentosResult.rows;
    console.log(chalk.cyan(`✅ ${knowledge.departamentos.length} departamentos ativos carregados`));

    // Mostrar exemplos de departamentos
    knowledge.departamentos.slice(0, 10).forEach((dept, index) => {
      console.log(`  ${index + 1}. ${dept.descricao || 'N/A'}`);
      if (dept.endereco) console.log(`     📍 ${dept.endereco}`);
      if (dept.horario_atendimento) console.log(`     🕒 ${dept.horario_atendimento}`);
      if (dept.email) console.log(`     📧 ${dept.email}`);
    });

    // 3. ANALISAR USUÁRIOS POR TIPO
    console.log(chalk.yellow('\n👥 Analisando perfil de usuários:'));
    
    // Contar usuários por tipo
    const userStatsResult = await client.query(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN servidor = true THEN 1 END) as servidores,
        COUNT(CASE WHEN conta_ativa = true THEN 1 END) as contas_ativas,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as com_email
      FROM usuarios
    `);

    knowledge.estatisticas.usuarios = userStatsResult.rows[0];
    console.log(chalk.cyan('Estatísticas de usuários:'));
    console.log(`  📊 Total: ${knowledge.estatisticas.usuarios.total_users}`);
    console.log(`  👔 Servidores: ${knowledge.estatisticas.usuarios.servidores}`);
    console.log(`  ✅ Contas Ativas: ${knowledge.estatisticas.usuarios.contas_ativas}`);
    console.log(`  📧 Com Email: ${knowledge.estatisticas.usuarios.com_email}`);

    // 4. BUSCAR TABELAS COM CONTEÚDO TEXTUAL
    console.log(chalk.yellow('\n📄 Buscando conteúdo textual relevante:'));
    
    const textualTables = [
      'documentos', 'processos', 'protocolos', 'solicitacoes', 
      'atendimentos', 'chamados', 'faq', 'noticias', 'artigos',
      'servicos', 'informacoes', 'avisos', 'comunicados'
    ];

    for (const tableName of textualTables) {
      try {
        const tableExists = await client.query(`
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_name = $1 AND table_schema = 'public'
        `, [tableName]);

        if (parseInt(tableExists.rows[0].count) > 0) {
          const sampleData = await client.query(`SELECT * FROM ${tableName} LIMIT 3`);
          if (sampleData.rows.length > 0) {
            knowledge.conhecimentoTextual.push({
              tabela: tableName,
              registros: sampleData.rowCount,
              amostra: sampleData.rows
            });
            console.log(chalk.green(`  ✅ ${tableName}: ${sampleData.rowCount} registros`));
          }
        }
      } catch (error) {
        // Tabela não existe, continuar
      }
    }

    // 5. ANALISAR ESTRUTURA ORGANIZACIONAL
    console.log(chalk.yellow('\n🏛️ Mapeando estrutura organizacional:'));
    
    // Departamentos por tipo
    const deptPorTipoResult = await client.query(`
      SELECT tipo, COUNT(*) as quantidade
      FROM departamentos 
      WHERE ativo = true AND tipo IS NOT NULL
      GROUP BY tipo
      ORDER BY quantidade DESC
    `);

    knowledge.estruturaOrganizacional.tiposDepartamento = deptPorTipoResult.rows;
    console.log(chalk.cyan('Tipos de departamentos:'));
    deptPorTipoResult.rows.forEach(row => {
      console.log(`  ${row.tipo}: ${row.quantidade} unidades`);
    });

    // 6. BUSCAR INFORMAÇÕES DE CONTATO E HORÁRIOS
    console.log(chalk.yellow('\n📞 Coletando informações de contato:'));
    
    const contatosResult = await client.query(`
      SELECT descricao, endereco, email, horario_atendimento
      FROM departamentos 
      WHERE ativo = true 
        AND (email IS NOT NULL OR endereco IS NOT NULL OR horario_atendimento IS NOT NULL)
      ORDER BY descricao
      LIMIT 20
    `);

    knowledge.padroes.contatos = contatosResult.rows;
    console.log(chalk.cyan(`✅ ${contatosResult.rows.length} departamentos com informações de contato`));

    // 7. ANALISAR PADRÕES DE NOMENCLATURA
    console.log(chalk.yellow('\n🔤 Analisando padrões de nomenclatura:'));
    
    const nomenclaturaResult = await client.query(`
      SELECT 
        CASE 
          WHEN descricao ILIKE '%secretaria%' THEN 'Secretaria'
          WHEN descricao ILIKE '%departamento%' THEN 'Departamento'
          WHEN descricao ILIKE '%coordenacao%' THEN 'Coordenação'
          WHEN descricao ILIKE '%assessoria%' THEN 'Assessoria'
          WHEN descricao ILIKE '%divisao%' THEN 'Divisão'
          ELSE 'Outros'
        END as categoria,
        COUNT(*) as quantidade
      FROM departamentos 
      WHERE ativo = true
      GROUP BY categoria
      ORDER BY quantidade DESC
    `);

    knowledge.padroes.nomenclatura = nomenclaturaResult.rows;
    console.log(chalk.cyan('Padrões de nomenclatura:'));
    nomenclaturaResult.rows.forEach(row => {
      console.log(`  ${row.categoria}: ${row.quantidade}`);
    });

    return knowledge;

  } catch (error) {
    console.error(chalk.red('❌ Erro na extração:'), error);
    throw error;
  } finally {
    await client.end();
  }
}

// Função para salvar conhecimento em arquivo
async function saveKnowledgeToFile(knowledge: MunicipalKnowledge) {
  const outputDir = path.join(__dirname, '..', '..', 'knowledge');
  
  // Criar diretório se não existir
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const outputFile = path.join(outputDir, 'municipal-knowledge.json');
  
  fs.writeFileSync(outputFile, JSON.stringify(knowledge, null, 2), 'utf8');
  
  console.log(chalk.green(`\n💾 Conhecimento salvo em: ${outputFile}`));
  
  // Criar também um resumo markdown
  const markdownFile = path.join(outputDir, 'municipal-knowledge-summary.md');
  const markdown = generateMarkdownSummary(knowledge);
  fs.writeFileSync(markdownFile, markdown, 'utf8');
  
  console.log(chalk.green(`📝 Resumo salvo em: ${markdownFile}`));
}

// Função para gerar resumo em markdown
function generateMarkdownSummary(knowledge: MunicipalKnowledge): string {
  return `# Conhecimento Municipal - Prefeitura de Valparaíso de Goiás

## Estatísticas Gerais
- **Total de Usuários**: ${knowledge.estatisticas.usuarios?.total_users || 'N/A'}
- **Servidores Públicos**: ${knowledge.estatisticas.usuarios?.servidores || 'N/A'}
- **Contas Ativas**: ${knowledge.estatisticas.usuarios?.contas_ativas || 'N/A'}
- **Departamentos Ativos**: ${knowledge.departamentos.length}

## Principais Departamentos
${knowledge.departamentos.slice(0, 20).map((dept, index) => 
  `${index + 1}. **${dept.descricao || 'N/A'}**${dept.endereco ? `\n   - 📍 ${dept.endereco}` : ''}${dept.horario_atendimento ? `\n   - 🕒 ${dept.horario_atendimento}` : ''}${dept.email ? `\n   - 📧 ${dept.email}` : ''}`
).join('\n\n')}

## Estrutura Organizacional
${knowledge.estruturaOrganizacional.tiposDepartamento?.map(tipo => 
  `- **${tipo.tipo}**: ${tipo.quantidade} unidades`
).join('\n') || 'N/A'}

## Padrões de Nomenclatura
${knowledge.padroes.nomenclatura?.map(nom => 
  `- **${nom.categoria}**: ${nom.quantidade} unidades`
).join('\n') || 'N/A'}

## Conteúdo Textual Disponível
${knowledge.conhecimentoTextual.map(conteudo => 
  `- **${conteudo.tabela}**: ${conteudo.registros} registros`
).join('\n') || 'Nenhum conteúdo textual específico encontrado'}

---
*Conhecimento extraído em: ${new Date().toLocaleString('pt-BR')}*
`;
}

// Executar extração
async function main() {
  try {
    const knowledge = await extractMunicipalKnowledge();
    await saveKnowledgeToFile(knowledge);
    
    console.log(chalk.green('\n🎉 Extração de conhecimento concluída com sucesso!'));
    console.log(chalk.blue('\n📋 Próximo passo: Usar este conhecimento para melhorar os prompts da DeepSeek API'));
    
  } catch (error) {
    console.error(chalk.red('\n❌ Erro na extração de conhecimento:'), error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

export { extractMunicipalKnowledge, MunicipalKnowledge };