#!/usr/bin/env node

/**
 * TESTE SIMPLES PARA WINDOWS
 * Execute este arquivo diretamente no PowerShell/CMD Windows
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testBasic() {
  console.log('🚀 TESTE BÁSICO - WINDOWS\n');
  
  // 1. Testar health
  console.log('1️⃣ Testando Health Check...');
  try {
    const health = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check OK:', health.data);
  } catch (error) {
    console.log('❌ Health Check falhou:', error.message);
    return;
  }
  
  // 2. Testar login
  console.log('\n2️⃣ Testando Login...');
  try {
    const login = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123',
      secretaria: 'administracao'
    });
    
    if (login.data.success) {
      console.log('✅ Login OK:', login.data.user);
      const token = login.data.token;
      
      // 3. Testar mensagem
      console.log('\n3️⃣ Testando Mensagem DeepSeek...');
      const msg = await axios.post(
        `${BASE_URL}/api/chat/message`,
        {
          message: 'Como solicitar alvará?',
          userId: 'test-user-1',
          secretaria: 'administracao'
        },
        {
          headers: { 'Authorization': `Bearer ${token}` }
        }
      );
      
      console.log('✅ Resposta recebida!');
      console.log('📄 Resposta:', msg.data.response?.substring(0, 100) + '...');
      console.log('💰 Custo:', msg.data.data?.cost || 'N/A');
      console.log('📊 Cache:', msg.data.cacheHit ? 'HIT' : 'MISS');
      
    } else {
      console.log('❌ Login falhou');
    }
  } catch (error) {
    console.log('❌ Erro:', error.response?.data || error.message);
  }
}

console.log('EXECUTE ESTE ARQUIVO NO WINDOWS:');
console.log('PowerShell: node test-windows.js');
console.log('');

testBasic();