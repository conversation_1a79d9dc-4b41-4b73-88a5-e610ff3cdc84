const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  console.log('=== VERIFICANDO USUÁRIOS NO POSTGRESQL ===\n');
  
  try {
    // Verificar conexão
    await prisma.$connect();
    console.log('✅ Conectado ao PostgreSQL!\n');
    
    // Buscar usuários com nome/email/cpf contendo "otto"
    console.log('1. Buscando usuários com "otto"...');
    const usersOtto = await prisma.$queryRaw`
      SELECT id, nome, email, cpf, login, conta_ativa, servidor
      FROM usuarios 
      WHERE LOWER(nome) LIKE '%otto%' 
         OR LOWER(email) LIKE '%otto%'
         OR LOWER(cpf) LIKE '%otto%'
         OR LOWER(login) LIKE '%otto%'
      LIMIT 10
    `;
    
    if (usersOtto.length > 0) {
      console.log(`\nEncontrados ${usersOtto.length} usuários com "otto":`);
      usersOtto.forEach(user => {
        console.log(`- ID: ${user.id}, Nome: ${user.nome}, Login: ${user.login}, Email: ${user.email}, CPF: ${user.cpf}, Ativo: ${user.conta_ativa}, Servidor: ${user.servidor}`);
      });
    } else {
      console.log('❌ Nenhum usuário encontrado com "otto"');
    }
    
    // Buscar usuários com "semantico"
    console.log('\n2. Buscando usuários com "semantico"...');
    const usersSemantico = await prisma.$queryRaw`
      SELECT id, nome, email, cpf, login, conta_ativa, servidor
      FROM usuarios 
      WHERE LOWER(nome) LIKE '%semantico%' 
         OR LOWER(email) LIKE '%semantico%'
         OR LOWER(login) LIKE '%semantico%'
      LIMIT 10
    `;
    
    if (usersSemantico.length > 0) {
      console.log(`\nEncontrados ${usersSemantico.length} usuários com "semantico":`);
      usersSemantico.forEach(user => {
        console.log(`- ID: ${user.id}, Nome: ${user.nome}, Login: ${user.login}, Email: ${user.email}, CPF: ${user.cpf}, Ativo: ${user.conta_ativa}`);
      });
    } else {
      console.log('❌ Nenhum usuário encontrado com "semantico"');
    }
    
    // Verificar estrutura da tabela
    console.log('\n3. Verificando estrutura da tabela usuarios...');
    const columns = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      ORDER BY ordinal_position
      LIMIT 20
    `;
    
    console.log('\nColunas principais da tabela usuarios:');
    columns.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // Contar total de usuários
    console.log('\n4. Estatísticas gerais...');
    const totalUsers = await prisma.$queryRaw`SELECT COUNT(*) as total FROM usuarios`;
    const activeUsers = await prisma.$queryRaw`SELECT COUNT(*) as total FROM usuarios WHERE conta_ativa = true`;
    const serverUsers = await prisma.$queryRaw`SELECT COUNT(*) as total FROM usuarios WHERE servidor = true`;
    
    console.log(`- Total de usuários: ${totalUsers[0].total}`);
    console.log(`- Usuários ativos: ${activeUsers[0].total}`);
    console.log(`- Servidores públicos: ${serverUsers[0].total}`);
    
    // Buscar alguns usuários de exemplo (primeiros 5 ativos)
    console.log('\n5. Exemplos de usuários ativos:');
    const exampleUsers = await prisma.$queryRaw`
      SELECT id, nome, email, cpf, login, servidor
      FROM usuarios 
      WHERE conta_ativa = true
      AND (email IS NOT NULL OR cpf IS NOT NULL)
      LIMIT 5
    `;
    
    exampleUsers.forEach(user => {
      console.log(`- Nome: ${user.nome}, Login: ${user.login || 'N/A'}, Email: ${user.email || 'N/A'}, CPF: ${user.cpf || 'N/A'}`);
    });
    
  } catch (error) {
    console.error('❌ Erro ao acessar PostgreSQL:', error.message);
    console.error('Detalhes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar verificação
checkUsers();