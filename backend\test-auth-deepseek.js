const axios = require('axios');

const API_BASE = 'http://localhost:3001';

async function testAuthAndDeepSeek() {
  console.log('=== TESTE DE AUTENTICAÇÃO E DEEPSEEK ===\n');
  
  // Senhas possíveis
  const passwords = ['123456', 'alfa5DELTA'];
  let loginSuccess = false;
  let token = null;
  let user = null;
  
  // Tentar login com ambas as senhas
  for (const password of passwords) {
    try {
      console.log(`1. Testando login com senha: ${password.substring(0, 3)}***`);
      
      const loginData = {
        login: 'otto',  // usando campo 'login' que aceita email ou CPF
        password: password,
        secretaria: 'administracao'
      };
      
      try {
        const loginResponse = await axios.post(`${API_BASE}/api/auth/login`, loginData);
        token = loginResponse.data.token;
        user = loginResponse.data.user;
        loginSuccess = true;
        console.log(`✅ Login bem-sucedido com a senha: ${password.substring(0, 3)}***`);
        break;
      } catch (error) {
        console.log(`❌ Falha com senha: ${password.substring(0, 3)}***`);
        if (error.response?.data?.error) {
          console.log(`   Erro: ${error.response.data.error}`);
        }
      }
    } catch (error) {
      console.log(`Erro ao tentar login: ${error.message}`);
    }
  }
  
  if (!loginSuccess) {
    console.error('\n❌ Nenhuma combinação de credenciais funcionou!');
    console.log('\nVerificando se o servidor está usando dados mock ou reais...');
    
    // Tentar com credenciais mock para diagnóstico
    try {
      const mockLogin = await axios.post(`${API_BASE}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123',
        secretaria: 'administracao'
      });
      console.log('⚠️  Sistema está usando dados MOCK, não dados reais do PostgreSQL');
      return;
    } catch (e) {
      console.log('Sistema não está aceitando dados mock também. Verifique a configuração.');
      return;
    }
  }
  
  console.log('\nUsuário autenticado:');
  console.log('- Nome:', user.name);
  console.log('- Role:', user.role);
  console.log('- Secretaria:', user.secretaria);
  console.log('- Token JWT:', token.substring(0, 50) + '...');
  
  // Passo 2: Fazer requisição ao chat/DeepSeek
  console.log('\n2. Fazendo requisição ao DeepSeek...');
  console.log('Pergunta: "Como solicitar alvará de construção?"');
  
  try {
    const chatResponse = await axios.post(
      `${API_BASE}/api/chat/message`,
      {
        message: 'Como solicitar alvará de construção?',
        secretaria: user.secretaria || 'administracao'
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log('\n✅ Resposta do DeepSeek recebida!');
    console.log('Resposta:', chatResponse.data.response.substring(0, 300) + '...');
    console.log('\nMetadados:');
    console.log('- Tokens usados:', chatResponse.data.metadata?.tokens?.total || 'N/A');
    console.log('- Custo: $', chatResponse.data.metadata?.cost?.total || 'N/A');
    console.log('- Cache hit:', chatResponse.data.cacheHit ? 'Sim' : 'Não');
    console.log('- Tempo de resposta:', chatResponse.data.metadata?.responseTime || 'N/A', 'ms');
    
    if (chatResponse.data.metadata?.rag) {
      console.log('- RAG ativo:', chatResponse.data.metadata.rag.enabled ? 'Sim' : 'Não');
      console.log('- Documentos RAG encontrados:', chatResponse.data.metadata.rag.documentsFound || 0);
    }
    
    // Passo 3: Verificar métricas
    console.log('\n3. Verificando métricas do sistema...');
    const metricsResponse = await axios.get(
      `${API_BASE}/api/chat/metrics`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log('\n✅ Métricas do sistema:');
    console.log('- Total de requisições:', metricsResponse.data.totalRequests);
    console.log('- Cache hits:', metricsResponse.data.cacheHits);
    console.log('- Taxa de cache:', metricsResponse.data.cacheHitRate);
    console.log('- Economia total: $', metricsResponse.data.totalSavings);
    
    // Passo 4: Testar segunda requisição (deve pegar do cache)
    console.log('\n4. Testando cache com mesma pergunta...');
    const cacheTestResponse = await axios.post(
      `${API_BASE}/api/chat/message`,
      {
        message: 'Como solicitar alvará de construção?',
        secretaria: user.secretaria || 'administracao'
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    console.log('✅ Segunda requisição:');
    console.log('- Cache hit:', cacheTestResponse.data.cacheHit ? '✅ SIM!' : '❌ NÃO');
    console.log('- Custo:', cacheTestResponse.data.metadata?.cost?.total || 0);
    console.log('- Economia:', cacheTestResponse.data.cacheHit ? '100% (resposta do cache)' : '0%');
    
    console.log('\n🎉 TESTE COMPLETO COM SUCESSO!');
    console.log('✅ Autenticação funcionando');
    console.log('✅ DeepSeek respondendo');
    console.log('✅ Sistema de cache operacional');
    console.log('✅ RAG vetorial ativo');
    console.log('\nO sistema está pronto para uso com dados reais da prefeitura!');
    
  } catch (error) {
    console.error('\n❌ Erro ao fazer requisição ao DeepSeek:', error.response?.data || error.message);
  }
}

// Executar teste
console.log('Iniciando teste com as credenciais fornecidas...\n');
testAuthAndDeepSeek();