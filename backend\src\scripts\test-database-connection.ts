/**
 * Script de teste de conexão com bancos de dados remotos
 * Verifica estrutura e permissões antes da integração
 */

import { Client } from 'pg';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import chalk from 'chalk';

// Carregar variáveis de ambiente
dotenv.config({ path: '../../.env' });

// Configurações dos bancos
const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

const mongoConfig = {
  host: '*************',
  port: 2711,
  user: 'mongodb',
  password: 'alfa0MEGA',
  database: 'pv_valparaiso'
};

/**
 * Testa conexão PostgreSQL
 */
async function testPostgreSQL() {
  console.log(chalk.blue('\n=== TESTANDO POSTGRESQL ===\n'));
  
  const client = new Client(postgresConfig);
  
  try {
    // Conectar
    await client.connect();
    console.log(chalk.green('✓ Conexão estabelecida com sucesso'));
    
    // Verificar usuário atual
    const userResult = await client.query('SELECT current_user, current_database()');
    console.log(chalk.cyan(`Usuário: ${userResult.rows[0].current_user}`));
    console.log(chalk.cyan(`Database: ${userResult.rows[0].current_database}`));
    
    // Listar todas as tabelas
    console.log(chalk.yellow('\n📋 Listando tabelas existentes:'));
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log(chalk.red('Nenhuma tabela encontrada no schema public'));
    } else {
      console.log(chalk.gray('Tabelas encontradas:'));
      tablesResult.rows.forEach(row => {
        console.log(`  - ${row.table_name}`);
      });
    }
    
    // Verificar se existe tabela users
    const usersTableResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `);
    
    if (usersTableResult.rows.length > 0) {
      console.log(chalk.yellow('\n🔍 Estrutura da tabela "users":'));
      usersTableResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
      });
    } else {
      console.log(chalk.red('\nTabela "users" não encontrada'));
    }
    
    // Verificar se existe tabela departments
    const deptTableResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'departments' OR table_name = 'secretarias'
      ORDER BY ordinal_position
    `);
    
    if (deptTableResult.rows.length > 0) {
      console.log(chalk.yellow('\n🔍 Estrutura da tabela "departments/secretarias":'));
      deptTableResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
      });
    } else {
      console.log(chalk.red('\nTabela "departments" ou "secretarias" não encontrada'));
    }
    
    // Testar permissões
    console.log(chalk.yellow('\n🔐 Testando permissões:'));
    
    // Teste de SELECT
    try {
      await client.query('SELECT 1');
      console.log(chalk.green('  ✓ SELECT permitido'));
    } catch (err) {
      console.log(chalk.red('  ✗ SELECT negado'));
    }
    
    // Teste de CREATE TABLE
    try {
      await client.query('CREATE TABLE test_permissions (id int)');
      await client.query('DROP TABLE test_permissions');
      console.log(chalk.green('  ✓ CREATE TABLE permitido'));
    } catch (err) {
      console.log(chalk.red('  ✗ CREATE TABLE negado'));
    }
    
    // Buscar por tabelas relacionadas ao sistema
    console.log(chalk.yellow('\n🔎 Buscando tabelas relacionadas ao sistema:'));
    const systemTablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND (
        table_name LIKE '%user%' 
        OR table_name LIKE '%pessoa%' 
        OR table_name LIKE '%funcionario%'
        OR table_name LIKE '%departamento%'
        OR table_name LIKE '%secretaria%'
        OR table_name LIKE '%setor%'
      )
      ORDER BY table_name
    `);
    
    if (systemTablesResult.rows.length > 0) {
      console.log(chalk.gray('Tabelas relacionadas encontradas:'));
      systemTablesResult.rows.forEach(row => {
        console.log(`  - ${row.table_name}`);
      });
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Erro ao conectar no PostgreSQL:'));
    console.error(error);
  } finally {
    await client.end();
  }
}

/**
 * Testa conexão MongoDB
 */
async function testMongoDB() {
  console.log(chalk.blue('\n=== TESTANDO MONGODB ===\n'));
  
  const mongoUri = `mongodb://${mongoConfig.user}:${mongoConfig.password}@${mongoConfig.host}:${mongoConfig.port}/${mongoConfig.database}`;
  
  try {
    // Conectar
    await mongoose.connect(mongoUri);
    console.log(chalk.green('✓ Conexão estabelecida com sucesso'));
    
    // Listar collections
    console.log(chalk.yellow('\n📋 Listando collections existentes:'));
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    if (collections.length === 0) {
      console.log(chalk.red('Nenhuma collection encontrada'));
    } else {
      console.log(chalk.gray('Collections encontradas:'));
      collections.forEach(col => {
        console.log(`  - ${col.name}`);
      });
    }
    
    // Verificar estatísticas do banco
    const stats = await mongoose.connection.db.stats();
    console.log(chalk.yellow('\n📊 Estatísticas do banco:'));
    console.log(`  - Tamanho: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`  - Collections: ${stats.collections}`);
    console.log(`  - Documentos: ${stats.objects}`);
    
    // Testar permissões
    console.log(chalk.yellow('\n🔐 Testando permissões:'));
    
    try {
      // Teste de leitura
      const testCollection = mongoose.connection.db.collection('test_permissions');
      await testCollection.findOne({});
      console.log(chalk.green('  ✓ READ permitido'));
      
      // Teste de escrita
      await testCollection.insertOne({ test: true, timestamp: new Date() });
      console.log(chalk.green('  ✓ WRITE permitido'));
      
      // Limpar teste
      await testCollection.deleteMany({ test: true });
      console.log(chalk.green('  ✓ DELETE permitido'));
    } catch (err) {
      console.log(chalk.red('  ✗ Permissões limitadas:', err.message));
    }
    
    // Buscar collections relacionadas
    console.log(chalk.yellow('\n🔎 Buscando collections relacionadas ao sistema:'));
    const relatedCollections = collections.filter(col => 
      col.name.includes('conversation') ||
      col.name.includes('message') ||
      col.name.includes('log') ||
      col.name.includes('chat') ||
      col.name.includes('user')
    );
    
    if (relatedCollections.length > 0) {
      console.log(chalk.gray('Collections relacionadas encontradas:'));
      relatedCollections.forEach(col => {
        console.log(`  - ${col.name}`);
      });
    }
    
  } catch (error) {
    console.log(chalk.red('❌ Erro ao conectar no MongoDB:'));
    console.error(error);
  } finally {
    await mongoose.disconnect();
  }
}

/**
 * Executar testes
 */
async function runTests() {
  console.log(chalk.magenta.bold('\n🔧 TESTE DE CONEXÃO COM BANCOS DE DADOS REMOTOS 🔧'));
  console.log(chalk.gray('=' . repeat(50)));
  
  await testPostgreSQL();
  await testMongoDB();
  
  console.log(chalk.magenta.bold('\n✅ TESTES CONCLUÍDOS'));
  console.log(chalk.gray('=' . repeat(50)));
  console.log(chalk.yellow('\n📝 Próximos passos:'));
  console.log('1. Analisar estrutura encontrada');
  console.log('2. Adaptar models/schemas conforme necessário');
  console.log('3. Implementar repositories compatíveis');
  console.log('4. Testar integração completa\n');
}

// Executar
runTests().catch(console.error);