import { Router } from 'express';
import DashboardController from '../controllers/dashboardController';

const router = Router();

/**
 * @route GET /api/dashboard/full
 * @desc Dashboard completo com todas as métricas
 * @access Private (Admin)
 */
router.get('/full', DashboardController.getFullDashboard);

/**
 * @route GET /api/dashboard/metrics
 * @desc Métricas simples para widgets
 * @access Private
 */
router.get('/metrics', DashboardController.getSimpleMetrics);

/**
 * @route GET /api/dashboard/savings
 * @desc Relatório de economia detalhado
 * @access Private
 * @query period: today|week|month
 */
router.get('/savings', DashboardController.getSavingsReport);

/**
 * @route GET /api/dashboard/status
 * @desc Status em tempo real do sistema
 * @access Private
 */
router.get('/status', DashboardController.getRealTimeStatus);

export default router;