import OpenAI from 'openai';
import { config } from 'dotenv';
import path from 'path';
import fs from 'fs';
import { DocumentChunk, RAGDataPreparation } from './ragDataPreparation';

config();

interface RAGSearchOptions {
  topK?: number;
  threshold?: number;
  secretaria?: string;
  tipo?: string;
  nivel_acesso?: string;
}

interface RAGSearchResult {
  documents: DocumentChunk[];
  distances: number[];
  metadata: any[];
}

interface VectorDocument {
  id: string;
  content: string;
  metadata: DocumentChunk['metadata'];
  embedding: number[];
}

export class SimpleRAGService {
  private openai: OpenAI;
  private vectorStore: VectorDocument[] = [];
  private isInitialized: boolean = false;
  private vectorStorePath: string;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.vectorStorePath = path.join(process.cwd(), 'rag-data', 'vector-store.json');
  }

  async initialize(): Promise<void> {
    try {
      console.log('🔄 Inicializando Simple RAG Service...');

      // Tentar carregar vector store existente
      if (fs.existsSync(this.vectorStorePath)) {
        const data = fs.readFileSync(this.vectorStorePath, 'utf-8');
        this.vectorStore = JSON.parse(data);
        console.log(`✅ Vector store carregado: ${this.vectorStore.length} documentos`);
      } else {
        console.log('ℹ️ Vector store não existe, será criado quando necessário');
      }

      this.isInitialized = true;
      console.log('✅ Simple RAG Service inicializado com sucesso');

    } catch (error) {
      console.error('❌ Erro ao inicializar Simple RAG:', error);
      throw new Error(`Falha na inicialização do Simple RAG: ${error.message}`);
    }
  }

  async addDocuments(documents: DocumentChunk[]): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log(`🔄 Vetorizando ${documents.length} documentos...`);

      const batchSize = 50; // Processar em lotes para evitar limites de API
      
      for (let i = 0; i < documents.length; i += batchSize) {
        const batch = documents.slice(i, i + batchSize);
        
        console.log(`🔄 Processando lote ${Math.floor(i / batchSize) + 1}/${Math.ceil(documents.length / batchSize)}`);

        // Preparar textos para embedding
        const texts = batch.map(doc => doc.content);

        // Criar embeddings para o lote
        const embeddingResponse = await this.openai.embeddings.create({
          model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
          input: texts
        });

        // Adicionar documentos ao vector store
        for (let j = 0; j < batch.length; j++) {
          const vectorDoc: VectorDocument = {
            id: batch[j].id,
            content: batch[j].content,
            metadata: batch[j].metadata,
            embedding: embeddingResponse.data[j].embedding
          };

          this.vectorStore.push(vectorDoc);
        }

        console.log(`✅ Lote ${Math.floor(i / batchSize) + 1} processado (${batch.length} documentos)`);
      }

      // Salvar vector store
      await this.saveVectorStore();

      console.log('✅ Todos os documentos foram vetorizados e armazenados');

    } catch (error) {
      console.error('❌ Erro ao adicionar documentos:', error);
      throw new Error(`Falha ao adicionar documentos: ${error.message}`);
    }
  }

  async searchSimilar(
    query: string, 
    options: RAGSearchOptions = {}
  ): Promise<RAGSearchResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (this.vectorStore.length === 0) {
      console.log('⚠️ Vector store vazio, execute initialize-rag.ts primeiro');
      return { documents: [], distances: [], metadata: [] };
    }

    try {
      const topK = options.topK || parseInt(process.env.RAG_TOP_K || '3');
      const threshold = options.threshold || parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7');

      console.log(`🔍 Buscando: "${query}" (top-${topK}, threshold: ${threshold})`);

      // Criar embedding da query
      const queryEmbedding = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
        input: query
      });

      const queryVector = queryEmbedding.data[0].embedding;

      // Filtrar documentos por metadata se especificado
      let candidateDocuments = this.vectorStore;

      if (options.secretaria) {
        candidateDocuments = candidateDocuments.filter(doc => 
          doc.metadata.secretaria === options.secretaria
        );
      }

      if (options.tipo) {
        candidateDocuments = candidateDocuments.filter(doc => 
          doc.metadata.tipo === options.tipo
        );
      }

      if (options.nivel_acesso) {
        candidateDocuments = candidateDocuments.filter(doc => 
          doc.metadata.nivel_acesso === options.nivel_acesso
        );
      }

      // Calcular similaridade coseno para todos os documentos candidatos
      const similarities = candidateDocuments.map(doc => ({
        document: doc,
        similarity: this.cosineSimilarity(queryVector, doc.embedding),
        distance: 1 - this.cosineSimilarity(queryVector, doc.embedding)
      }));

      // Ordenar por similaridade e aplicar threshold
      const relevantResults = similarities
        .filter(result => result.similarity >= threshold)
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, topK);

      // Preparar resposta
      const documents: DocumentChunk[] = relevantResults.map(result => ({
        id: result.document.id,
        content: result.document.content,
        metadata: result.document.metadata
      }));

      const distances = relevantResults.map(result => result.distance);
      const metadata = relevantResults.map(result => result.document.metadata);

      console.log(`✅ Encontrados ${documents.length} documentos relevantes`);

      return {
        documents,
        distances,
        metadata
      };

    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      throw new Error(`Falha na busca: ${error.message}`);
    }
  }

  async hybridSearch(
    query: string, 
    secretaria?: string,
    options: RAGSearchOptions = {}
  ): Promise<DocumentChunk[]> {
    try {
      console.log(`🔍 Busca híbrida: "${query}" | Secretaria: ${secretaria}`);

      // Busca semântica principal
      const semanticResults = await this.searchSimilar(query, {
        ...options,
        secretaria,
        topK: (options.topK || 3) * 2 // Buscar mais documentos inicialmente
      });

      // Busca por keywords se poucos resultados semânticos
      let keywordResults: DocumentChunk[] = [];
      if (semanticResults.documents.length < 2) {
        keywordResults = await this.keywordSearch(query, secretaria);
      }

      // Combinar e ranquear resultados
      const combinedResults = [
        ...semanticResults.documents,
        ...keywordResults.filter(kw => 
          !semanticResults.documents.some(sem => sem.id === kw.id)
        )
      ];

      // Ordenar por relevância e limitar resultados
      const finalResults = combinedResults
        .sort((a, b) => b.metadata.relevancia - a.metadata.relevancia)
        .slice(0, options.topK || 3);

      console.log(`✅ Busca híbrida: ${finalResults.length} documentos finais`);

      return finalResults;

    } catch (error) {
      console.error('❌ Erro na busca híbrida:', error);
      throw new Error(`Falha na busca híbrida: ${error.message}`);
    }
  }

  private async keywordSearch(query: string, secretaria?: string): Promise<DocumentChunk[]> {
    const keywords = query.toLowerCase().split(' ').filter(word => word.length > 2);
    
    let candidates = this.vectorStore;
    if (secretaria) {
      candidates = candidates.filter(doc => doc.metadata.secretaria === secretaria);
    }

    const keywordMatches = candidates.filter(doc => {
      const content = doc.content.toLowerCase();
      return keywords.some(keyword => content.includes(keyword));
    });

    return keywordMatches.slice(0, 10).map(doc => ({
      id: doc.id,
      content: doc.content,
      metadata: doc.metadata
    }));
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
    
    if (magnitudeA === 0 || magnitudeB === 0) return 0;
    
    return dotProduct / (magnitudeA * magnitudeB);
  }

  private async saveVectorStore(): Promise<void> {
    const vectorStoreDir = path.dirname(this.vectorStorePath);
    
    if (!fs.existsSync(vectorStoreDir)) {
      fs.mkdirSync(vectorStoreDir, { recursive: true });
    }

    fs.writeFileSync(this.vectorStorePath, JSON.stringify(this.vectorStore, null, 2));
    console.log(`💾 Vector store salvo: ${this.vectorStorePath}`);
  }

  async getStoreInfo(): Promise<any> {
    return {
      exists: this.vectorStore.length > 0,
      count: this.vectorStore.length,
      path: this.vectorStorePath,
      embedding_model: process.env.OPENAI_EMBEDDING_MODEL
    };
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.initialize();
      
      // Testar OpenAI API
      const testEmbedding = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
        input: 'teste de conectividade'
      });

      console.log('✅ Conexão OpenAI testada com sucesso');
      console.log('✅ Simple RAG inicializado com sucesso');
      
      return true;

    } catch (error) {
      console.error('❌ Erro no teste de conexão:', error);
      return false;
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.vectorStore.length > 0;
  }
}

// Instância singleton
let simpleRagServiceInstance: SimpleRAGService | null = null;

export function getSimpleRagService(): SimpleRAGService {
  if (!simpleRagServiceInstance) {
    simpleRagServiceInstance = new SimpleRAGService();
  }
  return simpleRagServiceInstance;
}

export default SimpleRAGService;