const { Client } = require('pg');

const client = new Client({
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
});

async function checkStructure() {
  console.log('=== VERIFICANDO ESTRUTURA DA TABELA USUARIOS ===\n');
  
  try {
    await client.connect();
    console.log('✅ Conectado ao PostgreSQL!\n');
    
    // 1. Listar todas as colunas da tabela usuarios
    console.log('1. Colunas da tabela usuarios:');
    const queryColumns = `
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      ORDER BY ordinal_position
    `;
    
    const resultColumns = await client.query(queryColumns);
    
    console.log(`\nTotal de colunas: ${resultColumns.rows.length}\n`);
    resultColumns.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // 2. Buscar usuários com nome "otto" (sem usar login)
    console.log('\n\n2. Buscando usuários com "otto" no nome, email ou CPF:');
    const queryOtto = `
      SELECT id, nome, email, cpf, conta_ativa, servidor
      FROM usuarios 
      WHERE LOWER(nome) LIKE '%otto%' 
         OR LOWER(email) LIKE '%otto%'
         OR cpf LIKE '%otto%'
      LIMIT 10
    `;
    
    const resultOtto = await client.query(queryOtto);
    
    if (resultOtto.rows.length > 0) {
      console.log(`\nEncontrados ${resultOtto.rows.length} usuários:`);
      resultOtto.rows.forEach(user => {
        console.log(`\nID: ${user.id}`);
        console.log(`Nome: ${user.nome}`);
        console.log(`Email: ${user.email || 'NULL'}`);
        console.log(`CPF: ${user.cpf || 'NULL'}`);
        console.log(`Conta Ativa: ${user.conta_ativa}`);
        console.log(`É Servidor: ${user.servidor}`);
      });
    } else {
      console.log('❌ Nenhum usuário encontrado com "otto"');
    }
    
    // 3. Verificar colunas relacionadas a senha
    console.log('\n\n3. Procurando colunas de senha:');
    const querySenha = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      AND (column_name LIKE '%senha%' OR column_name LIKE '%password%' OR column_name LIKE '%hash%')
    `;
    
    const resultSenha = await client.query(querySenha);
    
    if (resultSenha.rows.length > 0) {
      console.log('Colunas de senha encontradas:');
      resultSenha.rows.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type}`);
      });
    } else {
      console.log('❌ Nenhuma coluna de senha encontrada na tabela usuarios');
    }
    
    // 4. Verificar primeiros usuários ativos
    console.log('\n\n4. Primeiros 3 usuários ativos:');
    const queryExample = `
      SELECT id, nome, email, cpf
      FROM usuarios 
      WHERE conta_ativa = true
      AND email IS NOT NULL
      LIMIT 3
    `;
    
    const resultExample = await client.query(queryExample);
    
    resultExample.rows.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.nome}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   CPF: ${user.cpf || 'NULL'}`);
    });
    
  } catch (error) {
    console.error('\n❌ Erro:', error.message);
  } finally {
    await client.end();
    console.log('\n\n✅ Conexão fechada.');
  }
}

checkStructure();