import { Router, Request, Response } from 'express';
import { authService } from '../services/authService';

const router = Router();

/**
 * @route POST /api/auth/login
 * @desc Login do usuário com dados reais do PostgreSQL
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, cpf, login, password, secretaria } = req.body;

    // Aceitar tanto 'login' quanto 'email' ou 'cpf' para compatibilidade
    const userLogin = login || email || cpf;

    if (!userLogin || !password) {
      res.status(400).json({
        success: false,
        error: 'Login (email ou CPF) e senha são obrigatórios'
      });
      return;
    }

    // Fazer login usando AuthService real
    const loginResult = await authService.login({
      login: userLogin,
      password,
      secretaria
    });

    if (!loginResult.success) {
      res.status(401).json({
        success: false,
        error: loginResult.error
      });
      return;
    }

    res.json({
      success: true,
      token: loginResult.token,
      user: loginResult.user
    });

  } catch (error) {
    console.error('Erro no login:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor'
    });
  }
});

export default router;