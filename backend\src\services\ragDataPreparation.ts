import fs from 'fs';
import path from 'path';

interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    secretaria: string;
    tipo: 'departamento' | 'servico' | 'formulario' | 'lei' | 'procedimento' | 'estatistica';
    nivel_acesso: 'publico' | 'servidor' | 'admin';
    relevancia: number;
    keywords: string[];
    fonte: string;
  };
}

interface MunicipalData {
  departamentos?: any[];
  servicos?: any[];
  formularios?: any[];
  estatisticas?: any;
  conhecimentoTextual?: any[];
  padroes?: any;
}

interface ServicesData {
  servicos?: any[];
  formularios?: any[];
  solicitacoes?: any[];
  protocolos?: any[];
  procedimentos?: any[];
}

export class RAGDataPreparation {
  private chunkSize: number;
  private chunkOverlap: number;

  constructor() {
    this.chunkSize = parseInt(process.env.RAG_CHUNK_SIZE || '500');
    this.chunkOverlap = parseInt(process.env.RAG_CHUNK_OVERLAP || '50');
  }

  async prepareAllData(): Promise<DocumentChunk[]> {
    console.log('🔄 Iniciando preparação dos dados municipais para RAG...');
    
    const chunks: DocumentChunk[] = [];
    
    try {
      // Carregar dados municipais principais
      const municipalData = this.loadMunicipalKnowledge();
      const servicesData = this.loadServicesData();

      // Processar departamentos
      if (municipalData.departamentos) {
        const departmentChunks = this.processDepartments(municipalData.departamentos);
        chunks.push(...departmentChunks);
      }

      // Processar serviços
      if (servicesData.servicos) {
        const serviceChunks = this.processServices(servicesData.servicos);
        chunks.push(...serviceChunks);
      }

      // Processar formulários
      if (servicesData.formularios) {
        const formChunks = this.processForms(servicesData.formularios);
        chunks.push(...formChunks);
      }

      // Processar estatísticas
      if (municipalData.estatisticas) {
        const statsChunks = this.processStatistics(municipalData.estatisticas);
        chunks.push(...statsChunks);
      }

      // Processar conhecimento textual adicional
      if (municipalData.conhecimentoTextual) {
        const textChunks = this.processTextualKnowledge(municipalData.conhecimentoTextual);
        chunks.push(...textChunks);
      }

      console.log(`✅ Preparação concluída: ${chunks.length} chunks criados`);
      
      // Salvar chunks para referência
      await this.saveChunks(chunks);
      
      return chunks;

    } catch (error) {
      console.error('❌ Erro na preparação dos dados RAG:', error);
      throw new Error(`Falha na preparação dos dados: ${error.message}`);
    }
  }

  private loadMunicipalKnowledge(): MunicipalData {
    const knowledgePath = path.join(process.cwd(), 'knowledge', 'municipal-knowledge.json');
    
    if (!fs.existsSync(knowledgePath)) {
      console.warn('⚠️ Arquivo municipal-knowledge.json não encontrado');
      return {};
    }

    const data = fs.readFileSync(knowledgePath, 'utf-8');
    return JSON.parse(data);
  }

  private loadServicesData(): ServicesData {
    const servicesPath = path.join(process.cwd(), 'knowledge', 'services-deep-analysis.json');
    
    if (!fs.existsSync(servicesPath)) {
      console.warn('⚠️ Arquivo services-deep-analysis.json não encontrado');
      return {};
    }

    const data = fs.readFileSync(servicesPath, 'utf-8');
    return JSON.parse(data);
  }

  private processDepartments(departments: any[]): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    departments.forEach((dept) => {
      if (!dept.ativo || !dept.descricao) return;

      const content = this.createDepartmentContent(dept);
      const secretaria = this.mapDepartmentToSecretaria(dept.descricao);
      
      chunks.push({
        id: `dept_${dept.id}`,
        content,
        metadata: {
          secretaria,
          tipo: 'departamento',
          nivel_acesso: 'publico',
          relevancia: this.calculateRelevance(dept),
          keywords: this.extractKeywords(dept.descricao),
          fonte: 'postgresql_departments'
        }
      });
    });

    return chunks;
  }

  private processServices(services: any[]): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    services.forEach((service) => {
      if (!service.descricao) return;

      const content = this.createServiceContent(service);
      const secretaria = this.mapServiceToSecretaria(service.descricao);
      
      chunks.push({
        id: `service_${service.id}`,
        content,
        metadata: {
          secretaria,
          tipo: 'servico',
          nivel_acesso: 'publico',
          relevancia: this.calculateServiceRelevance(service),
          keywords: this.extractKeywords(service.descricao),
          fonte: 'postgresql_services'
        }
      });
    });

    return chunks;
  }

  private processForms(forms: any[]): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    forms.forEach((form) => {
      if (!form.nome) return;

      const content = this.createFormContent(form);
      const secretaria = this.mapFormToSecretaria(form.nome);
      
      chunks.push({
        id: `form_${form.id || form.nome.replace(/\s+/g, '_')}`,
        content,
        metadata: {
          secretaria,
          tipo: 'formulario',
          nivel_acesso: 'publico',
          relevancia: 0.8,
          keywords: this.extractKeywords(form.nome),
          fonte: 'postgresql_forms'
        }
      });
    });

    return chunks;
  }

  private processStatistics(stats: any): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    // Processar estatísticas gerais
    Object.entries(stats).forEach(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        const content = this.createStatisticsContent(key, value);
        
        chunks.push({
          id: `stats_${key}`,
          content,
          metadata: {
            secretaria: 'administracao',
            tipo: 'estatistica',
            nivel_acesso: 'servidor',
            relevancia: 0.6,
            keywords: [key, 'estatisticas', 'dados', 'municipal'],
            fonte: 'postgresql_statistics'
          }
        });
      }
    });

    return chunks;
  }

  private processTextualKnowledge(textualData: any[]): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];

    textualData.forEach((item, index) => {
      if (!item || typeof item !== 'object') return;

      const content = this.createTextualContent(item);
      
      chunks.push({
        id: `text_${index}`,
        content,
        metadata: {
          secretaria: 'geral',
          tipo: 'procedimento',
          nivel_acesso: 'publico',
          relevancia: 0.7,
          keywords: this.extractKeywordsFromObject(item),
          fonte: 'municipal_knowledge'
        }
      });
    });

    return chunks;
  }

  private createDepartmentContent(dept: any): string {
    const parts = [
      `Departamento: ${dept.descricao}`,
      dept.endereco ? `Endereço: ${dept.endereco}` : null,
      dept.horario_atendimento ? `Horário de Atendimento: ${dept.horario_atendimento}` : null,
      dept.email ? `Email: ${dept.email}` : null,
      dept.slogan ? `Descrição: ${dept.slogan}` : null,
      `Tipo: ${dept.tipo}`,
      `Status: ${dept.ativo ? 'Ativo' : 'Inativo'}`
    ].filter(Boolean);

    return parts.join('\n');
  }

  private createServiceContent(service: any): string {
    const parts = [
      `Serviço: ${service.descricao}`,
      `Cobra Taxa: ${service.cobrar_taxa ? 'Sim' : 'Não'}`,
      `Departamento ID: ${service.id_departamento}`,
      service.id_modelo_abertura_exigencia ? `Modelo Abertura: ${service.id_modelo_abertura_exigencia}` : null,
      service.id_modelo_despacho_exigencia ? `Modelo Despacho: ${service.id_modelo_despacho_exigencia}` : null
    ].filter(Boolean);

    return parts.join('\n');
  }

  private createFormContent(form: any): string {
    const parts = [
      `Formulário: ${form.nome}`,
      form.descricao ? `Descrição: ${form.descricao}` : null,
      form.uso ? `Uso: ${form.uso}` : null,
      form.procedimento ? `Procedimento: ${form.procedimento}` : null
    ].filter(Boolean);

    return parts.join('\n');
  }

  private createStatisticsContent(key: string, stats: any): string {
    const parts = [`Estatísticas de ${key}:`];
    
    Object.entries(stats).forEach(([statKey, statValue]) => {
      if (typeof statValue !== 'object') {
        parts.push(`${statKey}: ${statValue}`);
      }
    });

    return parts.join('\n');
  }

  private createTextualContent(item: any): string {
    if (typeof item === 'string') return item;
    
    const parts = [];
    Object.entries(item).forEach(([key, value]) => {
      if (typeof value === 'string' && value.trim()) {
        parts.push(`${key}: ${value}`);
      }
    });

    return parts.join('\n');
  }

  private mapDepartmentToSecretaria(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('educação') || desc.includes('escola') || desc.includes('creche')) return 'educacao';
    if (desc.includes('saúde') || desc.includes('ubs') || desc.includes('ambulat')) return 'saude';
    if (desc.includes('obra') || desc.includes('construção') || desc.includes('infraestrutura')) return 'obras';
    if (desc.includes('meio ambiente') || desc.includes('ambiental')) return 'meio_ambiente';
    if (desc.includes('assistência') || desc.includes('social') || desc.includes('cras')) return 'assistencia_social';
    if (desc.includes('finanças') || desc.includes('tribut') || desc.includes('arrecada')) return 'financas';
    
    return 'administracao';
  }

  private mapServiceToSecretaria(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('alvará') || desc.includes('construção') || desc.includes('obra')) return 'obras';
    if (desc.includes('fiscal') || desc.includes('postura')) return 'obras';
    if (desc.includes('credenciamento') || desc.includes('licenciamento')) return 'meio_ambiente';
    if (desc.includes('análise')) return 'administracao';
    
    return 'geral';
  }

  private mapFormToSecretaria(name: string): string {
    const desc = name.toLowerCase();
    
    if (desc.includes('alvará') || desc.includes('construção')) return 'obras';
    if (desc.includes('saúde') || desc.includes('vacinação')) return 'saude';
    if (desc.includes('escola') || desc.includes('educação')) return 'educacao';
    if (desc.includes('social') || desc.includes('assistência')) return 'assistencia_social';
    
    return 'geral';
  }

  private calculateRelevance(dept: any): number {
    let relevance = 0.5; // Base relevance
    
    if (dept.endereco) relevance += 0.1;
    if (dept.horario_atendimento) relevance += 0.1;
    if (dept.email) relevance += 0.1;
    if (dept.slogan) relevance += 0.1;
    
    return Math.min(relevance, 1.0);
  }

  private calculateServiceRelevance(service: any): number {
    let relevance = 0.7; // Services são mais relevantes
    
    if (service.cobrar_taxa) relevance += 0.1;
    if (service.id_modelo_abertura_exigencia) relevance += 0.1;
    if (service.id_modelo_despacho_exigencia) relevance += 0.1;
    
    return Math.min(relevance, 1.0);
  }

  private extractKeywords(text: string): string[] {
    const commonWords = ['de', 'da', 'do', 'das', 'dos', 'e', 'ou', 'para', 'com', 'em', 'no', 'na', 'nos', 'nas'];
    
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.includes(word))
      .slice(0, 10); // Limitar a 10 keywords
  }

  private extractKeywordsFromObject(obj: any): string[] {
    const text = JSON.stringify(obj).toLowerCase();
    return this.extractKeywords(text);
  }

  private async saveChunks(chunks: DocumentChunk[]): Promise<void> {
    const chunksDir = path.join(process.cwd(), 'rag-data', 'chunks');
    
    if (!fs.existsSync(chunksDir)) {
      fs.mkdirSync(chunksDir, { recursive: true });
    }

    const chunksPath = path.join(chunksDir, 'prepared-chunks.json');
    fs.writeFileSync(chunksPath, JSON.stringify(chunks, null, 2));
    
    console.log(`💾 Chunks salvos em: ${chunksPath}`);
  }
}

export { DocumentChunk };