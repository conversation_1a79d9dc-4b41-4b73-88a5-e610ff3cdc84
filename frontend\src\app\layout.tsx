import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Chatbot Inteligente - Secretarias',
  description: 'Sistema de Chatbot Inteligente para Secretarias da Prefeitura de Valparaíso de Goiás',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="pt-BR">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}