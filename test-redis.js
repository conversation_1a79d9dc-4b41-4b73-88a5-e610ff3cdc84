// Script de teste para verificar integração Redis
const Redis = require('ioredis');

async function testRedisIntegration() {
  console.log('🔍 === TESTE COMPLETO DO REDIS ===\n');

  // Configuração (mesma do projeto)
  const redisCache = new Redis({
    host: 'localhost',
    port: 6379,
    db: 0,
    lazyConnect: true
  });

  const redisQueue = new Redis({
    host: 'localhost', 
    port: 6379,
    db: 1,
    lazyConnect: true
  });

  try {
    // 1. Teste de Conexão
    console.log('1️⃣ Testando conexões...');
    await redisCache.ping();
    await redisQueue.ping();
    console.log('✅ Cache Redis: Conectado');
    console.log('✅ Queue Redis: Conectado\n');

    // 2. Teste de Cache Exato
    console.log('2️⃣ Testando cache exato...');
    const cacheKey = 'exact:teste_' + Date.now();
    const cacheValue = { 
      resposta: 'Como tirar alvará de construção?',
      secretaria: 'obras',
      timestamp: new Date().toISOString()
    };
    
    await redisCache.setex(cacheKey, 3600, JSON.stringify(cacheValue));
    const cached = await redisCache.get(cacheKey);
    console.log('✅ Cache salvo:', cacheKey);
    console.log('✅ Cache recuperado:', JSON.parse(cached).resposta);
    console.log('✅ TTL restante:', await redisCache.ttl(cacheKey), 'segundos\n');

    // 3. Teste de Filas
    console.log('3️⃣ Testando sistema de filas...');
    const queueKey = 'queue:immediate:teste_' + Date.now();
    const queueData = {
      userId: 'user123',
      message: 'Mensagem urgente',
      priority: 'immediate',
      timestamp: Date.now()
    };
    
    await redisQueue.lpush(queueKey, JSON.stringify(queueData));
    const queueItem = await redisQueue.rpop(queueKey);
    console.log('✅ Fila imediata testada');
    console.log('✅ Dados processados:', JSON.parse(queueItem).message);

    // 4. Teste de Múltiplos Databases
    console.log('\n4️⃣ Testando separação de databases...');
    
    // DB 0 (Cache)
    await redisCache.set('test_db0', 'dados_cache');
    
    // DB 1 (Queue)
    await redisQueue.set('test_db1', 'dados_fila');
    
    // Verificar separação
    const db0Keys = await redisCache.keys('test_*');
    const db1Keys = await redisQueue.keys('test_*');
    
    console.log('✅ DB 0 (Cache) - Chaves:', db0Keys);
    console.log('✅ DB 1 (Queue) - Chaves:', db1Keys);

    // 5. Teste de Horário de Desconto
    console.log('\n5️⃣ Testando sistema de descontos...');
    const now = new Date();
    const utcHour = now.getUTCHours() + now.getUTCMinutes() / 60;
    const isDiscount = utcHour >= 16.5 || utcHour < 0.5;
    
    console.log('🕐 Hora UTC atual:', utcHour.toFixed(2));
    console.log('💰 Período de desconto ativo:', isDiscount ? 'SIM (50% off)' : 'NÃO');
    
    if (isDiscount) {
      console.log('🎉 Economia de 50% está ativa até 00:30 UTC!');
    } else {
      const nextDiscount = utcHour < 16.5 ? 16.5 : 16.5 + 24;
      console.log('⏰ Próximo desconto às:', (nextDiscount % 24).toFixed(1), 'UTC');
    }

    // 6. Teste de Métricas
    console.log('\n6️⃣ Testando sistema de métricas...');
    const metricsKey = `metrics:cache_hits:${new Date().toISOString().split('T')[0]}`;
    await redisCache.incr(metricsKey);
    await redisCache.incr(metricsKey);
    await redisCache.incr(metricsKey);
    
    const hits = await redisCache.get(metricsKey);
    console.log('✅ Cache hits hoje:', hits);

    // 7. Limpeza
    console.log('\n7️⃣ Limpando dados de teste...');
    await redisCache.del(cacheKey, 'test_db0', metricsKey);
    await redisQueue.del('test_db1');
    console.log('✅ Cleanup concluído');

    // 8. Informações do Sistema
    console.log('\n8️⃣ Informações do sistema Redis...');
    const info = await redisCache.info('memory');
    const memoryMatch = info.match(/used_memory_human:([^\r\n]+)/);
    const memory = memoryMatch ? memoryMatch[1] : 'N/A';
    
    console.log('💾 Memória utilizada:', memory);
    console.log('🔑 Total de chaves DB0:', await redisCache.dbsize());
    console.log('🔑 Total de chaves DB1:', await redisQueue.dbsize());

    console.log('\n🎉 === TODOS OS TESTES PASSARAM ===');
    console.log('✅ Redis está configurado corretamente para o projeto');
    console.log('✅ Cache multi-camada funcionando');
    console.log('✅ Sistema de filas operacional');
    console.log('✅ Separação de databases OK');
    console.log('✅ Sistema de métricas ativo');

  } catch (error) {
    console.error('❌ Erro durante os testes:', error.message);
    console.log('\n🔧 Verifique se:');
    console.log('   - Redis está rodando (redis-server.exe)');
    console.log('   - Porta 6379 está disponível');
    console.log('   - Não há firewall bloqueando');
  } finally {
    await redisCache.quit();
    await redisQueue.quit();
    console.log('\n🔚 Conexões fechadas');
  }
}

// Executar testes
testRedisIntegration().catch(console.error);