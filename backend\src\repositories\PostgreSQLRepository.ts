/**
 * Repository para PostgreSQL usando estrutura real do banco
 * Baseado nas tabelas: usuarios, departamentos, user_departamentos
 */

import { Client, Pool } from 'pg';
import bcrypt from 'bcrypt';

interface Usuario {
  id: number;
  nome: string;
  email: string;
  cpf: string;
  senha: string;
  conta_ativa: boolean;
  servidor: boolean;
  created_at: Date;
  updated_at: Date;
}

interface Departamento {
  id: number;
  descricao: string;
  endereco?: string;
  email?: string;
  ativo: boolean;
  horario_atendimento?: string;
  created_at: Date;
  updated_at: Date;
}

interface UserDepartamento {
  id: number;
  id_user: number;
  id_departamento: number;
}

export class PostgreSQLRepository {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso',
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });
  }

  /**
   * Buscar usuário por email ou CPF
   */
  async findUserByEmailOrCPF(login: string): Promise<Usuario | null> {
    const client = await this.pool.connect();
    
    try {
      // Verificar se é email (contém @) ou CPF
      const isEmail = login.includes('@');
      const query = isEmail 
        ? 'SELECT * FROM usuarios WHERE email = $1 AND conta_ativa = true'
        : 'SELECT * FROM usuarios WHERE cpf = $1 AND conta_ativa = true';
      
      const result = await client.query(query, [login]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Usuario;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamentos do usuário
   */
  async getUserDepartments(userId: number): Promise<Departamento[]> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT d.*
        FROM departamentos d
        INNER JOIN user_departamentos ud ON d.id = ud.id_departamento
        WHERE ud.id_user = $1 AND d.ativo = true
      `;
      
      const result = await client.query(query, [userId]);
      return result.rows as Departamento[];
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamento por ID
   */
  async getDepartmentById(departmentId: number): Promise<Departamento | null> {
    const client = await this.pool.connect();
    
    try {
      const query = 'SELECT * FROM departamentos WHERE id = $1 AND ativo = true';
      const result = await client.query(query, [departmentId]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Departamento;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamento por descrição (nome)
   */
  async getDepartmentByName(name: string): Promise<Departamento | null> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT * FROM departamentos 
        WHERE LOWER(descricao) LIKE LOWER($1) AND ativo = true
        LIMIT 1
      `;
      const result = await client.query(query, [`%${name}%`]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Departamento;
    } finally {
      client.release();
    }
  }

  /**
   * Listar todos os departamentos ativos
   */
  async getAllDepartments(): Promise<Departamento[]> {
    const client = await this.pool.connect();
    
    try {
      const query = 'SELECT * FROM departamentos WHERE ativo = true ORDER BY descricao';
      const result = await client.query(query);
      return result.rows as Departamento[];
    } finally {
      client.release();
    }
  }

  /**
   * Validar senha do usuário usando bcrypt
   */
  async validatePassword(user: Usuario, password: string): Promise<boolean> {
    try {
      if (!user.senha) {
        return false;
      }
      
      // Validar com bcrypt (suporta tanto $2y$ quanto $2b$)
      const isValid = await bcrypt.compare(password, user.senha);
      return isValid;
    } catch (error) {
      console.error('Erro ao validar senha:', error);
      return false;
    }
  }

  /**
   * Atualizar último login do usuário
   */
  async updateLastLogin(userId: number): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      const query = 'UPDATE usuarios SET updated_at = NOW() WHERE id = $1';
      await client.query(query, [userId]);
    } finally {
      client.release();
    }
  }

  /**
   * Verificar se usuário tem acesso ao departamento
   */
  async userHasAccessToDepartment(userId: number, departmentId: number): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM user_departamentos 
        WHERE id_user = $1 AND id_departamento = $2
      `;
      
      const result = await client.query(query, [userId, departmentId]);
      return parseInt(result.rows[0].count) > 0;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar estatísticas gerais
   */
  async getStats() {
    const client = await this.pool.connect();
    
    try {
      const userCountResult = await client.query('SELECT COUNT(*) as total FROM usuarios WHERE conta_ativa = true');
      const departmentCountResult = await client.query('SELECT COUNT(*) as total FROM departamentos WHERE ativo = true');
      
      return {
        totalUsers: parseInt(userCountResult.rows[0].total),
        totalDepartments: parseInt(departmentCountResult.rows[0].total)
      };
    } finally {
      client.release();
    }
  }

  /**
   * Fechar conexões
   */
  async close(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Testar conexão
   */
  async testConnection(): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      await client.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Erro ao testar conexão PostgreSQL:', error);
      return false;
    } finally {
      client.release();
    }
  }
}

// Singleton instance
export const postgresRepository = new PostgreSQLRepository();