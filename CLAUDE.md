# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# REGRAS E PADRÕES DO PROJETO - VERSÃO OTIMIZADA

## 1. Diretrizes Essenciais
- **Idioma**: PT-BR obrigatório
- **Dados**: Proibido mock/hardcoded em produção
- **Nunca usar dados mockados ou hardcode em qualquer ambiente**
- **Nunca inserir dados manualmente em tabelas**: Toda inserção deve ser feita exclusivamente via frontend e utilizando as funções do sistema, para garantir que toda a lógica de negócio, validação e logging seja aplicada corretamente.
- **Comunicação**: Profissional
- **Nunca usar emojis em mensagens, logs, outputs ou qualquer lugar do sistema. Todo conteúdo deve ser sempre profissional.**
- **Acessibilidade**: WCAG 2.1 AA (aria-label, alt, contraste >4.5:1, teclado)

## 2. Segurança (Implementação Obrigatória)
- **Credenciais**: Apenas em `.env` (nunca versionar)
- **Validação**: Zod schemas em todas as rotas + sanitização XSS
- **Rate Limiting**: Por endpoint e tipo de operação
- **Autorização**: Role-based + isolamento por secretaria
- **CSRF**: Tokens automáticos em operações que modificam dados
- **Logging**: Mascarar dados sensíveis (senha→[REDACTED], JWT→eyJ...[REDACTED])
- **Senhas**: bcrypt + 12 salt rounds + política de força

### Rate Limits Configurados
```
Login: 5 tentativas/15min | Chat: 30 msgs/min | Dashboard: 50 req/5min
Admin: 10 ações/30min | Busca: 20 pesquisas/min | Export: 3/10min
```

### Funções de Validação Essenciais
```typescript
validateCPF(cpf: string): boolean
sanitizeInput(input: string): string  
generateCSRFToken(): string
authorize("admin", "gestor") // middleware
```

## 3. Arquitetura e Tecnologias

### Stack Principal
| Camada | Tecnologia | Propósito |
|--------|------------|-----------|
| **Backend** | Node.js 18 + Express + TypeScript + tsx | API REST |
| **Frontend** | Next.js 14 + React 18 + TypeScript + Tailwind | Interface |
| **DB Principal** | PostgreSQL + Prisma | Dados da aplicação |
| **DB Logs** | MongoDB + Mongoose | Conversas e auditoria |
| **Cache/Queue** | Redis + IORedis + Bull | Cache multi-camada + filas |
| **IA** | OpenAI SDK + DeepSeek V3 | Processamento de linguagem |
| **Auth** | JWT + bcrypt | Autenticação segura |
| **Validação** | Zod schemas | Input validation |
| **Estado** | Zustand + React Query | State management |
| **UI** | Radix UI + Tailwind + shadcn/ui | Componentes |
| **Formulários** | React Hook Form + Zod | Validação de forms |
| **Testes** | Jest + Supertest + Testing Library | Testing |

### Estrutura de Diretórios
```
backend/src/
├── controllers/    # Route handlers
├── services/      # Business logic  
├── repositories/  # Data access
├── schemas/       # Zod validation
├── middleware/    # Auth, validation, rate limiting
└── utils/         # Utilities

frontend/src/
├── containers/    # Smart components (lógica)
├── components/    # Dumb components (UI)
├── hooks/         # Custom hooks
├── contexts/      # Estado global
└── services/      # API clients
```

### Comandos Principais
```bash
# Desenvolvimento completo (root)
npm run dev              # Backend (3001) + Frontend (3000) via concurrently
npm run build           # Build backend + frontend
npm run start           # Start produção (backend apenas)
npm run test            # Testes backend + frontend
npm run install:all     # Instala deps root + backend + frontend

# Backend específico (/backend)
npm run dev             # Dev com tsx watch
npm run build           # Compilação TypeScript
npm run start           # Node.js em produção
npm run test            # Jest tests
npm run test:watch      # Jest em modo watch
npm run db:generate     # Prisma generate
npm run db:push         # Prisma db push
npm run db:migrate      # Prisma migrate dev
npm run db:studio       # Prisma Studio interface

# Frontend específico (/frontend)  
npm run dev             # Next.js dev server
npm run build           # Next.js build
npm run start           # Next.js start
npm run lint            # Next.js lint
npm run test            # Jest tests
npm run test:watch      # Jest em modo watch
npm run type-check      # TypeScript check sem emit
```

## 4. Padrões de Desenvolvimento

### Fluxo Obrigatório
1. **Branch** de feature
2. **Schema Zod** para validação
3. **Rate limiting** apropriado
4. **Endpoint backend** + validação
5. **Frontend** com error handling
6. **Testes** de segurança e integração

### Backend - Padrão de Controller
```typescript
router.post("/endpoint",
  authenticate,           // JWT validation
  authorize("admin"),     // Role check
  auditAccess("action"),  // Log access
  validateRequest(schema), // Zod validation
  controller             // Business logic
);
```

### Frontend - Padrão Container/Presentational

**Container (Smart):**
```typescript
export function FeatureContainer() {
  const logic = useFeatureLogic(); // Toda lógica aqui
  return <Feature {...presentationalProps} />;
}
```

**Component (Dumb):**
```typescript
export function Feature({ data, onAction, isLoading }) {
  return <div>{/* Apenas UI */}</div>; // Zero lógica
}
```

### Hooks - Regras Obrigatórias
```typescript
// SEMPRE no topo, nunca condicionais
function Component() {
  const [state] = useState(); // Sempre no topo
  const callback = useCallback(() => {}, [deps]); // Deps corretas
  const memoized = useMemo(() => calculation(), [deps]); // Evitar recálculo
  
  useEffect(() => {
    // Sempre cleanup
    return () => cleanup();
  }, []);
}
```

## 5. Performance e Otimização

### React - Memoização Obrigatória
```typescript
// Componentes puros
export const Component = React.memo(function Component({ data }) {
  const handler = useCallback((id) => onClick?.(id), [onClick]);
  const computed = useMemo(() => heavyCalc(data), [data]);
  return <div>{data.content}</div>;
});
```

### Listas Grandes - Virtualização
```typescript
// Para listas >50 itens
const { visibleItems } = useVirtualizedList(items, {
  itemHeight: 100,
  containerHeight: 400,
  overscan: 3
});
```

### Imagens - Otimização
```typescript
// Sempre usar componente otimizado
<OptimizedImage 
  src={src} 
  alt={alt}
  loading="lazy"    // Não críticas
  priority={true}   // Críticas (logos)
  quality={85}      // Otimizada
/>
```

## 6. Error Handling

### Error Boundaries (Multicamada)
```typescript
<PageErrorBoundary>      {/* Página inteira */}
  <SectionErrorBoundary> {/* Seções específicas */}
    <ComponentErrorBoundary> {/* Componentes críticos */}
      <CriticalComponent />
    </ComponentErrorBoundary>
  </SectionErrorBoundary>
</PageErrorBoundary>
```

### Enhanced API (Auto-notificação)
```typescript
const enhancedApi = useEnhancedApi();
// Automaticamente conecta erros com notificações visuais
const result = await enhancedApi.sendMessage(data);
```

### Error Recovery
```typescript
const { execute } = useErrorRecovery(asyncOperation, {
  maxRetries: 3,
  retryDelay: 1000,
  backoffFactor: 2
});
```

## 7. Autorização e Isolamento

### Matriz de Permissões
| Endpoint | admin | gestor | operador | consulta |
|----------|-------|--------|----------|----------|
| `/login` | ✅ | ✅ | ✅ | ✅ |
| `/system-info` | ✅ | ❌ | ❌ | ❌ |
| `/metrics` | ✅ | ✅ | ❌ | ❌ |
| `/message` | ✅ | ✅ | ✅ | ✅ |

### Isolamento Automático
```typescript
// Aplicado em todos os controllers
if (role === "admin") {
  filter = {}; // Vê tudo
} else if (role === "gestor") {
  filter = { secretaria }; // Apenas sua secretaria
} else {
  filter = { userId, secretaria }; // Apenas seus dados
}
```

## 8. Logging e Monitoramento

### Estrutura de Logs
```
logs/
├── app-YYYY-MM-DD.log              # Logs gerais
├── error-YYYY-MM-DD.log            # Erros
├── security-YYYY-MM-DD.log         # Eventos de segurança
└── security-critical-YYYY-MM-DD.log # Críticos
```

### Eventos Críticos Logados
- LOGIN_SUCCESS/FAILED_* | UNAUTHORIZED_ACCESS
- MULTIPLE_LOGIN_ATTEMPTS | CSRF_VIOLATION
- ADMIN_RESOURCE_ACCESS | TOKEN_REFRESHED

### Comandos de Monitoramento
```bash
tail -f backend/logs/security.log          # Eventos críticos
grep "LOGIN_FAILED" logs/security-*.log    # Tentativas falhas
grep "UNAUTHORIZED" logs/security-*.log    # Acessos negados
```

## 9. Ambiente e Deploy

### Variáveis Essenciais (.env)
```bash
# IA e Processamento
DEEPSEEK_API_KEY=        # DeepSeek V3 API integration
OPENAI_API_BASE_URL=     # DeepSeek endpoint (https://api.deepseek.com)

# Bancos de Dados
DATABASE_URL=            # PostgreSQL connection
MONGODB_URI=             # MongoDB connection
REDIS_URL=               # Redis connection (cache + queues)

# Autenticação e Segurança
JWT_SECRET=              # JWT token secret
JWT_EXPIRES_IN=          # Token expiration (default: 24h)

# Configuração de Ambiente
NODE_ENV=                # Environment (development/production)
PORT=                    # Server port (default: 3001)

# Cache e Performance
CACHE_TTL_EXACT=         # Exact cache TTL (default: 86400s)
CACHE_TTL_SEMANTIC=      # Semantic cache TTL (default: 43200s)
DISCOUNT_START_HOUR=     # UTC discount start (default: 16)
DISCOUNT_END_HOUR=       # UTC discount end (default: 0)
```

### Departamentos (Secretarias)
- Administração | Finanças | Saúde | Educação
- Obras e Urbanismo | Assistência Social | Meio Ambiente

### Conexões de Banco
- **PostgreSQL**: *************:5411 (db: `pv_valparaiso`)
- **MongoDB**: *************:2711 (db: conversas e logs)
- **Redis**: Configuração dual-database
  - DB 0: Cache (exact, semantic, context)
  - DB 1: Filas (Bull queues para processamento)

### Sistema de Autenticação Híbrido
```typescript
// Auto-detecção entre dados mock e reais
const userRepo = await createUserRepository();
// Funciona com mock (5 usuários teste) ou PostgreSQL

// Login flexível (email OU CPF)
POST /api/auth/login
{
  "email": "<EMAIL>",  // OU
  "cpf": "12345678900",                   // OU  
  "password": "admin123",
  "secretaria": "administracao"
}
```

### Sistema de Cache e Otimização de Custos
```typescript
// Cache multi-camada com TTL diferenciado
- Exact cache: MD5 hash (24h TTL) → 100% economia
- Semantic cache: AI similarity (12h TTL) → 100% economia  
- Context cache: Por secretaria (1h TTL) → Resposta otimizada
- Session cache: Por usuário (30min TTL) → Estado da conversa
```

### Sistema de Filas Inteligente
```typescript
// Dual-queue para otimização de custos
- Immediate queue: Processamento instantâneo (5 jobs simultâneos)
- Discount queue: Espera horário de desconto (10 jobs simultâneos)
- Auto-promotion: Mensagens urgentes sobem para fila imediata
- Classificação de urgência: IA determina prioridade automaticamente
```

### Otimização de Custos DeepSeek V3
```typescript
// Sistema de desconto automático
- Horário de desconto: UTC 16:30-00:30 (50% economia)
- Cache hit: $0 (economia total)
- Input token: $0.07/1M (normal) → $0.035/1M (desconto)
- Output token: $1.10/1M (normal) → $0.55/1M (desconto)
- Economia projetada: 60-70% dos custos totais
```

### IA DeepSeek - Prompts Especializados
- **7 prompts específicos** por secretaria municipal
- **Geração SQL** a partir de linguagem natural
- **Contexto conversacional** mantido no MongoDB
- **Cache semântico** com 95% de confiança para similaridade
- **Classificação de urgência** automática

## 10. Checklist de Qualidade

### Segurança
- [ ] `.env` no `.gitignore` + validação de variáveis
- [ ] Schemas Zod em todas as rotas
- [ ] Rate limiting configurado
- [ ] Autorização por role implementada
- [ ] CSRF protection ativo
- [ ] Logs com dados mascarados

### Frontend
- [ ] Hooks sempre no topo, deps corretas
- [ ] Componentes puros memoizados
- [ ] Error boundaries implementados
- [ ] Listas grandes virtualizadas
- [ ] Imagens otimizadas

### Backend
- [ ] Controllers com middleware completo
- [ ] Repositórios especializados (<200 linhas)
- [ ] Validação + sanitização em todas as rotas
- [ ] Logging de eventos críticos

### Testes e Desenvolvimento
```bash
# Testes unitários
cd backend && npm run test          # Jest backend
cd frontend && npm run test         # Jest frontend

# Testes em modo watch
cd backend && npm run test:watch    # Watch mode backend
cd frontend && npm run test:watch   # Watch mode frontend

# Banco de dados
cd backend && npm run db:generate   # Gera cliente Prisma
cd backend && npm run db:push       # Sync schema com DB
cd backend && npm run db:migrate    # Cria migration
cd backend && npm run db:studio     # Interface visual Prisma

# Type checking
cd backend && tsc --noEmit          # TypeScript check backend
cd frontend && npm run type-check   # TypeScript check frontend

# API Testing Examples
# Testar autenticação
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123","secretaria":"administracao"}'

# Testar chat com cache
curl -X POST http://localhost:3001/api/chat/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -d '{"message":"Como fazer solicitação de alvará?","secretaria":"administracao"}'

# Verificar status da fila
curl -X GET http://localhost:3001/api/chat/queue/<USER_ID> \
  -H "Authorization: Bearer <JWT_TOKEN>"

# Métricas de cache e performance
curl -X GET http://localhost:3001/api/chat/metrics \
  -H "Authorization: Bearer <JWT_TOKEN>"
```

## 11. Comandos de Debugging e Desenvolvimento

### Monitoramento em Tempo Real
```bash
# Logs de sistema em tempo real  
tail -f backend/logs/app-$(date +%Y-%m-%d).log
tail -f backend/logs/security-$(date +%Y-%m-%d).log

# Monitorar Redis (cache hits/misses)
redis-cli monitor | grep -E "(GET|SET|DEL)"

# Métricas de performance via API
curl -s http://localhost:3001/api/chat/metrics | jq .

# Status das filas Bull
curl -s http://localhost:3001/api/chat/queue/status | jq .
```

### Debugging de Cache
```bash
# Verificar chaves no Redis
redis-cli -n 0 KEYS "*cache*"     # Cache database
redis-cli -n 1 KEYS "*queue*"     # Queue database

# Estatísticas de cache
redis-cli -n 0 INFO keyspace

# Limpar cache específico
redis-cli -n 0 DEL "exact_cache:*"
redis-cli -n 0 DEL "semantic_cache:*"
```

### Análise de Performance
```bash
# Métricas de CPU e Memória
top -p $(pgrep -f "node.*backend")

# Conexões de banco de dados
cd backend && npm run db:studio    # Interface visual
psql $DATABASE_URL -c "SELECT * FROM pg_stat_activity;"

# Análise de custos da IA
grep "COST_CALCULATION" backend/logs/app-*.log | tail -20
```

## 12. Sistema de Documentação

### Documentação Técnica Detalhada
- **[/docs/README.md](./docs/README.md)** - Índice completo da documentação
- **[/docs/redis-architecture.md](./docs/redis-architecture.md)** - Arquitetura completa do Redis
- **[/docs/](./docs/)** - Diretório com toda documentação técnica

### Guias Específicos Disponíveis
- **Redis Architecture**: Cache multi-camada + sistema de filas + otimização de custos
- **Database Architecture**: PostgreSQL + MongoDB + Redis integration
- **API Documentation**: Endpoints, schemas e exemplos práticos
- **Local Setup**: Configuração completa do ambiente de desenvolvimento

### Acesso Rápido à Documentação
```bash
# Ver documentação Redis completa
cat docs/redis-architecture.md

# Índice de toda documentação
cat docs/README.md

# Estrutura do projeto
ls -la docs/
```

## 13. MCP Servers Disponíveis
- **PostgreSQL MCP**: Acesso direto ao banco principal
- **MongoDB MCP**: Acesso ao banco de conversas  
- **Redis MCP**: Análise de cache e filas em desenvolvimento
- Útil para debugging e análise de dados

---

**Esta versão otimizada mantém 100% das informações técnicas essenciais em formato conciso para máxima eficiência de leitura pela IA.**