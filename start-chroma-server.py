#!/usr/bin/env python3
"""
Script para iniciar servidor ChromaDB localmente
Instale primeiro: pip install chromadb
Execute: python start-chroma-server.py
"""

import chromadb
from chromadb.config import Settings
import uvicorn
import os

def start_chroma_server():
    print("🚀 Iniciando servidor ChromaDB...")
    
    # Configurar diretório de persistência
    persist_directory = os.path.join(os.getcwd(), 'rag-data', 'chroma-db')
    os.makedirs(persist_directory, exist_ok=True)
    
    print(f"📁 Diretório de dados: {persist_directory}")
    
    # Configurações do servidor
    settings = Settings(
        chroma_api_impl="chromadb.api.fastapi.FastAPI",
        chroma_server_host="0.0.0.0",
        chroma_server_http_port=8000,
        persist_directory=persist_directory,
        allow_reset=True
    )
    
    # Iniciar servidor
    print("🌐 Servidor ChromaDB rodando em http://localhost:8000")
    print("✋ Pressione Ctrl+C para parar")
    
    try:
        uvicorn.run(
            "chromadb.app:app",
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Servidor ChromaDB interrompido")

if __name__ == "__main__":
    start_chroma_server()