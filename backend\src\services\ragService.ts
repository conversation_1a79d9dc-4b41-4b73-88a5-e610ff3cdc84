import OpenAI from 'openai';
import { config } from 'dotenv';
import path from 'path';
import { DocumentChunk, RAGDataPreparation } from './ragDataPreparation';

config();

interface RAGSearchOptions {
  topK?: number;
  threshold?: number;
  secretaria?: string;
  tipo?: string;
  nivel_acesso?: string;
}

interface RAGSearchResult {
  documents: DocumentChunk[];
  distances: number[];
  metadata: any[];
}

export class RAGService {
  private chromaClient: any;
  private collection: any = null;
  private openai: OpenAI;
  private embedder: any;
  private isInitialized: boolean = false;
  private collectionName: string = 'municipal_knowledge';

  constructor() {
    // Configurar cliente OpenAI para embeddings
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Configurar embedding function e cliente Chroma
    try {
      const { ChromaClient, OpenAIEmbeddingFunction } = require('chromadb');
      
      this.embedder = new OpenAIEmbeddingFunction({
        openai_api_key: process.env.OPENAI_API_KEY!,
        openai_model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small'
      });

      this.chromaClient = new ChromaClient({
        path: "http://localhost:8000"
      });
    } catch (error) {
      console.error('❌ Erro ao inicializar ChromaDB:', error);
      throw new Error(`Falha na inicialização do ChromaDB: ${error.message}`);
    }
  }

  async initialize(): Promise<void> {
    try {
      console.log('🔄 Inicializando serviço RAG...');

      // Verificar se a collection já existe
      try {
        this.collection = await this.chromaClient.getCollection({
          name: this.collectionName,
          embeddingFunction: this.embedder
        });
        console.log('✅ Collection existente carregada');
      } catch (error) {
        // Collection não existe, será criada no primeiro uso
        console.log('ℹ️ Collection não existe ainda, será criada quando necessário');
      }

      this.isInitialized = true;
      console.log('✅ Serviço RAG inicializado com sucesso');

    } catch (error) {
      console.error('❌ Erro ao inicializar RAG:', error);
      throw new Error(`Falha na inicialização do RAG: ${error.message}`);
    }
  }

  async createCollection(): Promise<void> {
    try {
      console.log('🔄 Criando collection Chroma...');

      this.collection = await this.chromaClient.createCollection({
        name: this.collectionName,
        embeddingFunction: this.embedder,
        metadata: {
          description: 'Conhecimento municipal da Prefeitura de Valparaíso de Goiás',
          created_at: new Date().toISOString(),
          version: '1.0'
        }
      });

      console.log('✅ Collection criada com sucesso');

    } catch (error) {
      console.error('❌ Erro ao criar collection:', error);
      throw new Error(`Falha na criação da collection: ${error.message}`);
    }
  }

  async addDocuments(documents: DocumentChunk[]): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.collection) {
      await this.createCollection();
    }

    try {
      console.log(`🔄 Adicionando ${documents.length} documentos à base vetorial...`);

      // Preparar dados para inserção
      const ids = documents.map(doc => doc.id);
      const contents = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => ({
        secretaria: doc.metadata.secretaria,
        tipo: doc.metadata.tipo,
        nivel_acesso: doc.metadata.nivel_acesso,
        relevancia: doc.metadata.relevancia,
        keywords: JSON.stringify(doc.metadata.keywords),
        fonte: doc.metadata.fonte
      }));

      // Inserir em lotes para evitar limitações de API
      const batchSize = 50;
      for (let i = 0; i < documents.length; i += batchSize) {
        const batchIds = ids.slice(i, i + batchSize);
        const batchContents = contents.slice(i, i + batchSize);
        const batchMetadatas = metadatas.slice(i, i + batchSize);

        await this.collection!.add({
          ids: batchIds,
          documents: batchContents,
          metadatas: batchMetadatas
        });

        console.log(`✅ Lote ${Math.floor(i / batchSize) + 1} adicionado (${batchIds.length} documentos)`);
      }

      console.log('✅ Todos os documentos foram adicionados à base vetorial');

    } catch (error) {
      console.error('❌ Erro ao adicionar documentos:', error);
      throw new Error(`Falha ao adicionar documentos: ${error.message}`);
    }
  }

  async searchSimilar(
    query: string, 
    options: RAGSearchOptions = {}
  ): Promise<RAGSearchResult> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.collection) {
      throw new Error('Collection não encontrada. Execute initialize-rag.ts primeiro.');
    }

    try {
      const topK = options.topK || parseInt(process.env.RAG_TOP_K || '3');
      const threshold = options.threshold || parseFloat(process.env.RAG_SIMILARITY_THRESHOLD || '0.7');

      // Construir filtros de metadata
      const whereClause: any = {};
      if (options.secretaria) {
        whereClause.secretaria = options.secretaria;
      }
      if (options.tipo) {
        whereClause.tipo = options.tipo;
      }
      if (options.nivel_acesso) {
        whereClause.nivel_acesso = options.nivel_acesso;
      }

      console.log(`🔍 Buscando: "${query}" (top-${topK}, threshold: ${threshold})`);

      // Realizar busca semântica
      const results = await this.collection.query({
        queryTexts: [query],
        nResults: topK,
        where: Object.keys(whereClause).length > 0 ? whereClause : undefined
      });

      // Processar resultados
      const documents: DocumentChunk[] = [];
      const distances: number[] = [];
      const metadata: any[] = [];

      if (results.documents && results.documents[0]) {
        for (let i = 0; i < results.documents[0].length; i++) {
          const distance = results.distances?.[0]?.[i] || 1;
          
          // Aplicar threshold de similaridade
          if (distance <= (1 - threshold)) {
            const doc: DocumentChunk = {
              id: results.ids?.[0]?.[i] || `unknown_${i}`,
              content: results.documents[0][i] || '',
              metadata: {
                secretaria: results.metadatas?.[0]?.[i]?.secretaria || 'unknown',
                tipo: results.metadatas?.[0]?.[i]?.tipo as any || 'procedimento',
                nivel_acesso: results.metadatas?.[0]?.[i]?.nivel_acesso as any || 'publico',
                relevancia: results.metadatas?.[0]?.[i]?.relevancia || 0.5,
                keywords: JSON.parse(results.metadatas?.[0]?.[i]?.keywords || '[]'),
                fonte: results.metadatas?.[0]?.[i]?.fonte || 'unknown'
              }
            };

            documents.push(doc);
            distances.push(distance);
            metadata.push(results.metadatas?.[0]?.[i]);
          }
        }
      }

      console.log(`✅ Encontrados ${documents.length} documentos relevantes`);

      return {
        documents,
        distances,
        metadata
      };

    } catch (error) {
      console.error('❌ Erro na busca semântica:', error);
      throw new Error(`Falha na busca: ${error.message}`);
    }
  }

  async hybridSearch(
    query: string, 
    secretaria?: string,
    options: RAGSearchOptions = {}
  ): Promise<DocumentChunk[]> {
    try {
      console.log(`🔍 Busca híbrida: "${query}" | Secretaria: ${secretaria}`);

      // Busca semântica principal
      const semanticResults = await this.searchSimilar(query, {
        ...options,
        secretaria,
        topK: (options.topK || 3) * 2 // Buscar mais documentos inicialmente
      });

      // Busca por keywords se poucos resultados semânticos
      let keywordResults: DocumentChunk[] = [];
      if (semanticResults.documents.length < 2) {
        keywordResults = await this.keywordSearch(query, secretaria);
      }

      // Combinar e ranquear resultados
      const combinedResults = [
        ...semanticResults.documents,
        ...keywordResults.filter(kw => 
          !semanticResults.documents.some(sem => sem.id === kw.id)
        )
      ];

      // Ordenar por relevância e limitar resultados
      const finalResults = combinedResults
        .sort((a, b) => b.metadata.relevancia - a.metadata.relevancia)
        .slice(0, options.topK || 3);

      console.log(`✅ Busca híbrida: ${finalResults.length} documentos finais`);

      return finalResults;

    } catch (error) {
      console.error('❌ Erro na busca híbrida:', error);
      throw new Error(`Falha na busca híbrida: ${error.message}`);
    }
  }

  private async keywordSearch(query: string, secretaria?: string): Promise<DocumentChunk[]> {
    // Implementação básica de busca por keywords
    // Em um cenário real, isso poderia usar um índice de texto completo
    const keywords = query.toLowerCase().split(' ').filter(word => word.length > 2);
    
    const results = await this.searchSimilar('', {
      secretaria,
      topK: 20 // Buscar mais documentos para filtrar por keywords
    });

    return results.documents.filter(doc => {
      const content = doc.content.toLowerCase();
      return keywords.some(keyword => content.includes(keyword));
    });
  }

  async getCollectionInfo(): Promise<any> {
    if (!this.collection) {
      return { exists: false, count: 0 };
    }

    try {
      const count = await this.collection.count();
      return {
        exists: true,
        count,
        name: this.collectionName,
        embedding_model: process.env.OPENAI_EMBEDDING_MODEL
      };
    } catch (error) {
      return { exists: false, count: 0, error: error.message };
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.initialize();
      
      // Testar OpenAI API
      const testEmbedding = await this.openai.embeddings.create({
        model: process.env.OPENAI_EMBEDDING_MODEL || 'text-embedding-3-small',
        input: 'teste de conectividade'
      });

      console.log('✅ Conexão OpenAI testada com sucesso');
      console.log('✅ Chroma DB inicializado com sucesso');
      
      return true;

    } catch (error) {
      console.error('❌ Erro no teste de conexão:', error);
      return false;
    }
  }

  isReady(): boolean {
    return this.isInitialized && this.collection !== null;
  }
}

// Instância singleton
let ragServiceInstance: RAGService | null = null;

export function getRagService(): RAGService {
  if (!ragServiceInstance) {
    ragServiceInstance = new RAGService();
  }
  return ragServiceInstance;
}

export default RAGService;