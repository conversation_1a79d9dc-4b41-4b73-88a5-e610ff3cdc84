/**
 * Serviço de Autenticação Real usando PostgreSQL
 * Substitui o sistema mock por dados reais
 */

import jwt from 'jsonwebtoken';
import { postgresRepository } from '../repositories/PostgreSQLRepository';

interface LoginCredentials {
  login: string; // email ou CPF
  password: string;
  secretaria?: string; // opcional para compatibilidade
}

interface AuthenticatedUser {
  id: number;
  name: string;
  email: string;
  cpf: string;
  role: string;
  secretaria: string;
  departmentId: number;
}

interface LoginResponse {
  success: boolean;
  token?: string;
  user?: AuthenticatedUser;
  error?: string;
}

export class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';
  private readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

  /**
   * Realizar login com dados reais do PostgreSQL
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      // Buscar usuário no banco
      const user = await postgresRepository.findUserByEmailOrCPF(credentials.login);
      
      if (!user) {
        return {
          success: false,
          error: 'Usuário não encontrado ou conta inativa'
        };
      }

      // Validar senha
      const isValidPassword = await postgresRepository.validatePassword(user, credentials.password);
      
      if (!isValidPassword) {
        return {
          success: false,
          error: 'Senha incorreta'
        };
      }

      // Buscar departamentos do usuário
      const departments = await postgresRepository.getUserDepartments(user.id);
      
      if (departments.length === 0) {
        return {
          success: false,
          error: 'Usuário não possui departamentos associados'
        };
      }

      // Se foi especificada uma secretaria, validar
      let selectedDepartment = departments[0]; // Padrão: primeiro departamento
      
      if (credentials.secretaria) {
        const foundDept = departments.find(dept => 
          dept.descricao.toLowerCase().includes(credentials.secretaria!.toLowerCase())
        );
        
        if (foundDept) {
          selectedDepartment = foundDept;
        } else {
          return {
            success: false,
            error: 'Usuário não tem acesso a esta secretaria'
          };
        }
      }

      // Determinar role baseado no tipo de usuário
      const role = this.determineUserRole(user, selectedDepartment);

      // Criar objeto do usuário autenticado
      const authenticatedUser: AuthenticatedUser = {
        id: user.id,
        name: user.nome,
        email: user.email || '',
        cpf: user.cpf,
        role: role,
        secretaria: this.mapDepartmentToSecretaria(selectedDepartment.descricao),
        departmentId: selectedDepartment.id
      };

      // Gerar JWT
      const token = jwt.sign(
        {
          id: authenticatedUser.id,
          name: authenticatedUser.name,
          email: authenticatedUser.email,
          role: authenticatedUser.role,
          secretaria: authenticatedUser.secretaria,
          departmentId: authenticatedUser.departmentId
        },
        this.JWT_SECRET,
        { expiresIn: this.JWT_EXPIRES_IN }
      );

      // Atualizar último login
      await postgresRepository.updateLastLogin(user.id);

      return {
        success: true,
        token,
        user: authenticatedUser
      };

    } catch (error) {
      console.error('Erro no login:', error);
      return {
        success: false,
        error: 'Erro interno no servidor'
      };
    }
  }

  /**
   * Verificar se token JWT é válido
   */
  async verifyToken(token: string): Promise<AuthenticatedUser | null> {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      
      // Verificar se usuário ainda está ativo
      const user = await postgresRepository.findUserByEmailOrCPF(decoded.email || decoded.cpf);
      
      if (!user || !user.conta_ativa) {
        return null;
      }

      return {
        id: decoded.id,
        name: decoded.name,
        email: decoded.email,
        cpf: decoded.cpf,
        role: decoded.role,
        secretaria: decoded.secretaria,
        departmentId: decoded.departmentId
      };
    } catch (error) {
      console.error('Erro ao verificar token:', error);
      return null;
    }
  }

  /**
   * Determinar role do usuário baseado em suas características
   */
  private determineUserRole(user: any, department: any): string {
    // Se é servidor público
    if (user.servidor) {
      // Verificar se é responsável pelo departamento
      if (department.id_usuario_responsavel === user.id) {
        return 'gestor';
      }
      return 'operador';
    }
    
    // Se não é servidor, role de consulta
    return 'consulta';
  }

  /**
   * Mapear nome do departamento para código da secretaria
   */
  private mapDepartmentToSecretaria(descricao: string): string {
    const desc = descricao.toLowerCase();
    
    if (desc.includes('administra')) return 'administracao';
    if (desc.includes('finan')) return 'financas';
    if (desc.includes('saude') || desc.includes('saúde')) return 'saude';
    if (desc.includes('educa')) return 'educacao';
    if (desc.includes('obra') || desc.includes('urban')) return 'obras';
    if (desc.includes('assist') || desc.includes('social')) return 'assistencia_social';
    if (desc.includes('meio') || desc.includes('ambient')) return 'meio_ambiente';
    
    // Padrão: usar primeiras palavras do nome
    return desc.replace(/\s+/g, '_').substring(0, 20);
  }

  /**
   * Listar usuários para admin (limitado)
   */
  async listUsers(requestingUser: AuthenticatedUser, limit: number = 50) {
    try {
      // Apenas admins podem listar usuários
      if (requestingUser.role !== 'admin') {
        throw new Error('Acesso negado');
      }

      // Implementar listagem se necessário
      const stats = await postgresRepository.getStats();
      return stats;
    } catch (error) {
      console.error('Erro ao listar usuários:', error);
      throw error;
    }
  }

  /**
   * Logout (invalidar token)
   * Nota: Como JWT é stateless, só podemos invalidar no cliente
   */
  async logout(): Promise<{ success: boolean }> {
    // Em JWT stateless, logout é feito removendo token do cliente
    // Para invalidação real, precisaríamos de blacklist no Redis
    return { success: true };
  }
}

// Singleton instance
export const authService = new AuthService();