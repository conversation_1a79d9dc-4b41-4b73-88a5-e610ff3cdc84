#!/usr/bin/env tsx

import { config } from 'dotenv';
import { RAGDataPreparation } from '../services/ragDataPreparation';
import { getSimpleRagService } from '../services/simpleRAGService';
import chalk from 'chalk';
import path from 'path';

// Carregar variáveis de ambiente do diretório backend
config({ path: path.join(__dirname, '../../../.env') });

async function initializeSimpleRAG() {
  console.log(chalk.blue('🚀 Inicializando Simple RAG System - Prefeitura de Valparaíso'));
  console.log(chalk.gray('=' .repeat(60)));

  const startTime = Date.now();

  try {
    // Verificar variáveis essenciais
    console.log(chalk.yellow('📋 Verificando configurações...'));
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY não configurada no arquivo .env');
    }

    console.log(chalk.green('✅ Configurações verificadas'));

    // Passo 1: Testar conectividade
    console.log(chalk.yellow('\n🔌 Testando conectividade...'));
    
    const ragService = getSimpleRagService();
    const connectionTest = await ragService.testConnection();
    
    if (!connectionTest) {
      throw new Error('Falha no teste de conectividade com OpenAI');
    }

    console.log(chalk.green('✅ Conectividade testada com sucesso'));

    // Passo 2: Preparar dados municipais
    console.log(chalk.yellow('\n📊 Preparando dados municipais...'));
    
    const dataPreparation = new RAGDataPreparation();
    const chunks = await dataPreparation.prepareAllData();

    if (chunks.length === 0) {
      throw new Error('Nenhum chunk de dados foi gerado. Verifique os arquivos de conhecimento.');
    }

    console.log(chalk.green(`✅ ${chunks.length} chunks de dados preparados`));

    // Mostrar estatísticas dos chunks
    const statsBySecretaria = chunks.reduce((acc, chunk) => {
      acc[chunk.metadata.secretaria] = (acc[chunk.metadata.secretaria] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(chalk.cyan('\n📈 Distribuição por Secretaria:'));
    Object.entries(statsBySecretaria).forEach(([secretaria, count]) => {
      console.log(chalk.cyan(`   ${secretaria}: ${count} chunks`));
    });

    const statsByTipo = chunks.reduce((acc, chunk) => {
      acc[chunk.metadata.tipo] = (acc[chunk.metadata.tipo] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(chalk.cyan('\n📋 Distribuição por Tipo:'));
    Object.entries(statsByTipo).forEach(([tipo, count]) => {
      console.log(chalk.cyan(`   ${tipo}: ${count} chunks`));
    });

    // Passo 3: Vetorizar e armazenar documentos
    console.log(chalk.yellow('\n🧠 Vetorizando documentos (isso pode levar alguns minutos)...'));
    
    const embeddingStartTime = Date.now();
    await ragService.addDocuments(chunks);
    const embeddingTime = (Date.now() - embeddingStartTime) / 1000;

    console.log(chalk.green(`✅ Vetorização concluída em ${embeddingTime.toFixed(1)}s`));

    // Passo 4: Validar instalação
    console.log(chalk.yellow('\n🧪 Validando instalação...'));
    
    // Teste básico de busca
    const testQuery = 'Como solicitar alvará de construção?';
    console.log(chalk.gray(`   Teste: "${testQuery}"`));
    
    const testResults = await ragService.searchSimilar(testQuery, { topK: 2 });
    
    if (testResults.documents.length === 0) {
      throw new Error('Teste de busca falhou - nenhum documento encontrado');
    }

    console.log(chalk.green(`✅ Teste de busca: ${testResults.documents.length} documentos encontrados`));
    
    // Mostrar primeiro resultado como exemplo
    const firstResult = testResults.documents[0];
    console.log(chalk.cyan(`   Melhor resultado: ${firstResult.metadata.tipo} da ${firstResult.metadata.secretaria}`));
    console.log(chalk.gray(`   Relevância: ${(firstResult.metadata.relevancia * 100).toFixed(1)}%`));
    console.log(chalk.gray(`   Similaridade: ${((1 - testResults.distances[0]) * 100).toFixed(1)}%`));

    // Passo 5: Informações finais
    const totalTime = (Date.now() - startTime) / 1000;
    
    console.log(chalk.green('\n' + '='.repeat(60)));
    console.log(chalk.green('🎉 SIMPLE RAG SYSTEM INICIALIZADO COM SUCESSO!'));
    console.log(chalk.green('='.repeat(60)));
    
    console.log(chalk.white('\n📊 Resumo da Implementação:'));
    console.log(chalk.white(`   • Documentos vetorizados: ${chunks.length}`));
    console.log(chalk.white(`   • Secretarias cobertas: ${Object.keys(statsBySecretaria).length}`));
    console.log(chalk.white(`   • Tipos de conteúdo: ${Object.keys(statsByTipo).length}`));
    console.log(chalk.white(`   • Tempo total: ${totalTime.toFixed(1)}s`));
    console.log(chalk.white(`   • Tempo de vetorização: ${embeddingTime.toFixed(1)}s`));

    // Estimativa de custo
    const estimatedTokens = chunks.length * 100; // Estimativa média de tokens por chunk
    const estimatedCost = (estimatedTokens / 1000000) * 0.02; // $0.02 per 1M tokens
    console.log(chalk.white(`   • Custo estimado: $${estimatedCost.toFixed(4)}`));

    console.log(chalk.cyan('\n🔧 Próximos Passos:'));
    console.log(chalk.cyan('   1. O Simple RAG está pronto para uso'));
    console.log(chalk.cyan('   2. Execute: npm run dev (backend integrado)'));
    console.log(chalk.cyan('   3. Teste no chat com perguntas específicas'));
    console.log(chalk.cyan('   4. RAG funciona automaticamente com fallback'));

    console.log(chalk.green('\n✨ Simple RAG System Ready! ✨\n'));

  } catch (error) {
    console.error(chalk.red('\n❌ ERRO NA INICIALIZAÇÃO DO SIMPLE RAG:'));
    console.error(chalk.red(`   ${error.message}`));
    
    if (error.stack) {
      console.error(chalk.gray('\n📋 Stack trace:'));
      console.error(chalk.gray(error.stack));
    }

    console.log(chalk.yellow('\n🔧 Possíveis soluções:'));
    console.log(chalk.yellow('   • Verificar se a chave OpenAI está correta'));
    console.log(chalk.yellow('   • Verificar se os arquivos de conhecimento existem'));
    console.log(chalk.yellow('   • Verificar conectividade com a internet'));

    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  initializeSimpleRAG();
}

export { initializeSimpleRAG };