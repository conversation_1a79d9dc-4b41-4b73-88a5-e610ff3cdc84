#!/bin/bash

echo "🚀 Iniciando Sistema RAG Completo - Prefeitura de Valparaiso"
echo

echo "📋 Verificando dependências..."

# Verificar se Docker está instalado
if command -v docker &> /dev/null; then
    echo "✅ Docker encontrado, usando container ChromaDB..."
    echo
    echo "🐳 Iniciando container ChromaDB..."
    docker-compose up -d chromadb
    echo
    echo "⏳ Aguardando ChromaDB inicializar..."
    sleep 10
elif command -v python3 &> /dev/null; then
    echo "✅ Python encontrado, instalando ChromaDB..."
    pip3 install chromadb uvicorn
    echo
    echo "🌐 Iniciando servidor ChromaDB via Python..."
    python3 start-chroma-server.py &
    CHROMA_PID=$!
    echo "ChromaDB PID: $CHROMA_PID"
    sleep 5
else
    echo "❌ Nem Docker nem Python encontrados"
    echo "💡 Por favor instale Docker ou Python 3.8+"
    echo "   Docker: https://docs.docker.com/get-docker/"
    echo "   Python: https://www.python.org/downloads/"
    exit 1
fi

echo
echo "🧪 Testando conectividade ChromaDB..."
if curl -s http://localhost:8000/api/v1/heartbeat > /dev/null 2>&1; then
    echo "✅ ChromaDB está rodando em http://localhost:8000"
else
    echo "❌ ChromaDB não está respondendo em http://localhost:8000"
    echo "🔧 Verifique se o servidor iniciou corretamente"
    exit 1
fi

echo
echo "🔄 Inicializando base vetorial..."
cd backend
npx tsx src/scripts/initialize-rag.ts

if [ $? -eq 0 ]; then
    echo
    echo "🎉 SISTEMA RAG PRONTO!"
    echo
    echo "📋 Serviços ativos:"
    echo "   • ChromaDB: http://localhost:8000"
    echo "   • Backend: http://localhost:3001 (quando executar npm run dev)"
    echo
    echo "🚀 Para iniciar o backend:"
    echo "   cd backend && npm run dev"
    echo
    echo "🧪 Para testar o RAG:"
    echo "   cd backend && npx tsx src/scripts/test-rag-comparison.ts"
    echo
else
    echo
    echo "❌ Erro na inicialização do RAG"
    exit 1
fi