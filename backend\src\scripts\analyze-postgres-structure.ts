/**
 * Script para analisar estrutura detalhada das tabelas PostgreSQL
 */

import { Client } from 'pg';
import chalk from 'chalk';

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function analyzePostgresTables() {
  console.log(chalk.blue('\n=== ANÁLISE DETALHADA POSTGRESQL ===\n'));
  
  const client = new Client(postgresConfig);
  
  try {
    await client.connect();
    
    // Analisar tabela departamentos
    console.log(chalk.yellow('🏢 Estrutura da tabela "departamentos":'));
    const deptResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'departamentos'
      ORDER BY ordinal_position
    `);
    
    if (deptResult.rows.length > 0) {
      deptResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
      });
      
      // Mostrar alguns dados da tabela departamentos
      const deptData = await client.query('SELECT * FROM departamentos LIMIT 10');
      console.log(chalk.cyan('\n📊 Primeiros 10 registros de departamentos:'));
      deptData.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ID: ${row.id}, Nome: ${row.nome || row.name || 'N/A'}`);
      });
    } else {
      console.log(chalk.red('Tabela "departamentos" não encontrada'));
    }
    
    // Analisar tabela usuarios (diferente de users)
    console.log(chalk.yellow('\n👤 Estrutura da tabela "usuarios":'));
    const usuariosResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'usuarios'
      ORDER BY ordinal_position
    `);
    
    if (usuariosResult.rows.length > 0) {
      usuariosResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
      });
      
      // Mostrar alguns dados da tabela usuarios
      const usuariosData = await client.query('SELECT * FROM usuarios LIMIT 5');
      console.log(chalk.cyan('\n📊 Primeiros 5 registros de usuarios:'));
      usuariosData.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ID: ${row.id}, Nome: ${row.nome || row.name || 'N/A'}, Email: ${row.email || 'N/A'}`);
      });
    } else {
      console.log(chalk.red('Tabela "usuarios" não encontrada'));
    }
    
    // Analisar tabela user_departamentos
    console.log(chalk.yellow('\n🔗 Estrutura da tabela "user_departamentos":'));
    const userDeptResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'user_departamentos'
      ORDER BY ordinal_position
    `);
    
    if (userDeptResult.rows.length > 0) {
      userDeptResult.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''} ${col.column_default ? `DEFAULT ${col.column_default}` : ''}`);
      });
    } else {
      console.log(chalk.red('Tabela "user_departamentos" não encontrada'));
    }
    
    // Verificar alguns dados da tabela users
    console.log(chalk.yellow('\n👥 Amostra da tabela "users":'));
    const usersData = await client.query('SELECT id, nome, email, cpf, matricula, conta_ativa, ativo FROM users LIMIT 5');
    console.log(chalk.cyan('Primeiros 5 usuários:'));
    usersData.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. ID: ${row.id}, Nome: ${row.nome}, Email: ${row.email}, CPF: ${row.cpf}, Ativo: ${row.ativo}`);
    });
    
    // Contar registros
    const userCount = await client.query('SELECT COUNT(*) as total FROM users');
    const deptCount = await client.query('SELECT COUNT(*) as total FROM departamentos');
    
    console.log(chalk.green(`\n📈 Estatísticas:`));
    console.log(`  - Total de usuários: ${userCount.rows[0].total}`);
    console.log(`  - Total de departamentos: ${deptCount.rows[0].total}`);
    
  } catch (error) {
    console.log(chalk.red('❌ Erro na análise:'));
    console.error(error);
  } finally {
    await client.end();
  }
}

// Executar análise
analyzePostgresTables().catch(console.error);