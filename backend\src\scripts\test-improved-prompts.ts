/**
 * Teste dos prompts melhorados da DeepSeek API
 */

import { processMessage } from '../services/deepSeekService';
import chalk from 'chalk';

interface TestCase {
  secretaria: string;
  pergunta: string;
  esperado: string[];
}

const testCases: TestCase[] = [
  {
    secretaria: 'administracao',
    pergunta: 'Como faço para ser admitido como servidor municipal?',
    esperado: ['ADMISSÃO DE SERVIDOR', 'formulário', '111.396 cidadãos', 'ARQUIVO-RH']
  },
  {
    secretaria: 'obras',
    pergunta: 'Preciso de alvará para construir uma casa. Que documentos preciso?',
    esperado: ['ALVARÁ DE CONSTRUÇÃO', 'projeto arquitetônico', 'Lei Complementar', 'assinados e escaneados']
  },
  {
    secretaria: 'financas',
    pergunta: 'Como consulto meus débitos municipais?',
    esperado: ['CONSULTA DE DÉBITOS', 'CPF/CNPJ', 'sistema online', 'parcelamento']
  },
  {
    secretaria: 'obras',
    pergunta: 'Tem um buraco na rua da minha casa, como solicito reparo?',
    esperado: ['TAPA BURACO', 'DIVISÃO DE SERVIÇOS GERAIS', 'localização', 'asfalto']
  },
  {
    secretaria: 'meio_ambiente',
    pergunta: 'Preciso podar uma árvore no meu quintal',
    esperado: ['PODA DE ÁRVORE', 'localização exata', 'avaliação técnica', 'endereço completo']
  },
  {
    secretaria: 'assistencia_social',
    pergunta: 'Como me cadastro para receber benefícios sociais?',
    esperado: ['Questionário Familiar', 'CadÚnico', 'documentação', 'CRAS']
  }
];

async function testImprovedPrompts() {
  console.log(chalk.blue('\n=== TESTE DOS PROMPTS MELHORADOS ===\n'));
  
  let successCount = 0;
  let totalTests = testCases.length;

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(chalk.yellow(`\n📋 Teste ${i + 1}/${totalTests}: ${testCase.secretaria.toUpperCase()}`));
    console.log(chalk.cyan(`❓ Pergunta: ${testCase.pergunta}`));
    
    try {
      const context = {
        userId: 'test-user',
        secretaria: testCase.secretaria,
        history: [],
        metadata: {
          userName: 'Teste',
          userRole: 'test'
        }
      };

      const startTime = Date.now();
      const response = await processMessage(testCase.pergunta, context);
      const endTime = Date.now();
      const processingTime = endTime - startTime;

      console.log(chalk.green(`⏱️  Tempo de resposta: ${processingTime}ms`));
      console.log(chalk.green(`💰 Custo: $${response.cost.withDiscount.toFixed(6)}`));
      console.log(chalk.green(`🎯 Tokens: ${response.tokens.total}`));
      
      // Verificar se a resposta contém as palavras-chave esperadas
      const responseText = response.content.toLowerCase();
      let foundKeywords = 0;
      
      console.log(chalk.cyan('\n📝 Resposta:'));
      console.log(response.content);
      
      console.log(chalk.cyan('\n🔍 Verificando palavras-chave esperadas:'));
      testCase.esperado.forEach(keyword => {
        const found = responseText.includes(keyword.toLowerCase());
        if (found) {
          foundKeywords++;
          console.log(chalk.green(`  ✅ "${keyword}" - ENCONTRADO`));
        } else {
          console.log(chalk.red(`  ❌ "${keyword}" - NÃO ENCONTRADO`));
        }
      });

      const keywordScore = (foundKeywords / testCase.esperado.length) * 100;
      
      if (keywordScore >= 50) {
        successCount++;
        console.log(chalk.green(`\n✅ TESTE PASSOU - Score: ${keywordScore.toFixed(1)}%`));
      } else {
        console.log(chalk.red(`\n❌ TESTE FALHOU - Score: ${keywordScore.toFixed(1)}%`));
      }
      
    } catch (error) {
      console.log(chalk.red(`\n❌ ERRO NO TESTE: ${error.message}`));
    }
    
    console.log(chalk.gray('\n' + '='.repeat(80)));
  }

  // Resumo final
  console.log(chalk.blue('\n=== RESUMO DOS TESTES ==='));
  console.log(chalk.cyan(`📊 Testes executados: ${totalTests}`));
  console.log(chalk.green(`✅ Sucessos: ${successCount}`));
  console.log(chalk.red(`❌ Falhas: ${totalTests - successCount}`));
  
  const successRate = (successCount / totalTests) * 100;
  
  if (successRate >= 80) {
    console.log(chalk.green(`\n🎉 EXCELENTE! Taxa de sucesso: ${successRate.toFixed(1)}%`));
    console.log(chalk.green('✨ Os prompts melhorados estão funcionando muito bem!'));
  } else if (successRate >= 60) {
    console.log(chalk.yellow(`\n👍 BOM! Taxa de sucesso: ${successRate.toFixed(1)}%`));
    console.log(chalk.yellow('🔧 Há espaço para melhorias nos prompts.'));
  } else {
    console.log(chalk.red(`\n😕 PRECISA MELHORAR! Taxa de sucesso: ${successRate.toFixed(1)}%`));
    console.log(chalk.red('🛠️  Os prompts precisam de mais ajustes.'));
  }
}

// Executar testes
if (require.main === module) {
  testImprovedPrompts().catch(console.error);
}

export { testImprovedPrompts };