/**
 * Script de teste da integração sem bcrypt (para resolver problema de compatibilidade)
 */

import { postgresRepository } from '../repositories/PostgreSQLRepository';
import chalk from 'chalk';

async function testSimpleAuth() {
  console.log(chalk.blue('\n=== TESTE SIMPLES DE INTEGRAÇÃO PostgreSQL ===\n'));

  try {
    // 1. Testar conexão
    console.log(chalk.yellow('1. Testando conexão PostgreSQL...'));
    const isConnected = await postgresRepository.testConnection();
    
    if (!isConnected) {
      console.log(chalk.red('❌ Falha na conexão PostgreSQL'));
      return;
    }
    console.log(chalk.green('✅ PostgreSQL conectado com sucesso'));

    // 2. Buscar estatísticas
    console.log(chalk.yellow('\n2. Estatísticas do banco:'));
    const stats = await postgresRepository.getStats();
    console.log(chalk.cyan(`   - Usuários ativos: ${stats.totalUsers}`));
    console.log(chalk.cyan(`   - Departamentos ativos: ${stats.totalDepartments}`));

    // 3. Listar alguns departamentos
    console.log(chalk.yellow('\n3. Primeiros 10 departamentos:'));
    const departments = await postgresRepository.getAllDepartments();
    departments.slice(0, 10).forEach((dept, index) => {
      console.log(chalk.gray(`   ${index + 1}. [${dept.id}] ${dept.descricao}`));
    });

    // 4. Buscar usuário por email
    console.log(chalk.yellow('\n4. Testando busca de usuário por email:'));
    const userByEmail = await postgresRepository.findUserByEmailOrCPF('<EMAIL>');
    
    if (userByEmail) {
      console.log(chalk.green('✅ Usuário encontrado por email:'));
      console.log(chalk.cyan(`   - ID: ${userByEmail.id}`));
      console.log(chalk.cyan(`   - Nome: ${userByEmail.nome}`));
      console.log(chalk.cyan(`   - Email: ${userByEmail.email}`));
      console.log(chalk.cyan(`   - CPF: ${userByEmail.cpf}`));
      console.log(chalk.cyan(`   - Ativo: ${userByEmail.conta_ativa}`));
      console.log(chalk.cyan(`   - Servidor: ${userByEmail.servidor}`));

      // Buscar departamentos do usuário
      console.log(chalk.yellow('\n5. Departamentos do usuário:'));
      const userDepts = await postgresRepository.getUserDepartments(userByEmail.id);
      
      if (userDepts.length > 0) {
        console.log(chalk.green(`✅ Encontrados ${userDepts.length} departamentos:`));
        userDepts.forEach((dept, index) => {
          console.log(chalk.cyan(`   ${index + 1}. [${dept.id}] ${dept.descricao}`));
        });
      } else {
        console.log(chalk.red('❌ Usuário não possui departamentos associados'));
      }
    } else {
      console.log(chalk.red('❌ Usuário não encontrado'));
    }

    // 6. Buscar usuário por CPF
    console.log(chalk.yellow('\n6. Testando busca de usuário por CPF:'));
    const userByCPF = await postgresRepository.findUserByEmailOrCPF('737.118.101-87');
    
    if (userByCPF) {
      console.log(chalk.green('✅ Usuário encontrado por CPF:'));
      console.log(chalk.cyan(`   - Nome: ${userByCPF.nome}`));
      console.log(chalk.cyan(`   - Email: ${userByCPF.email}`));
    } else {
      console.log(chalk.orange('⚠️ Usuário não encontrado por CPF'));
    }

    // 7. Teste de departamento por nome
    console.log(chalk.yellow('\n7. Testando busca de departamento por nome:'));
    const deptByName = await postgresRepository.getDepartmentByName('saude');
    
    if (deptByName) {
      console.log(chalk.green('✅ Departamento encontrado:'));
      console.log(chalk.cyan(`   - ID: ${deptByName.id}`));
      console.log(chalk.cyan(`   - Nome: ${deptByName.descricao}`));
      console.log(chalk.cyan(`   - Email: ${deptByName.email || 'N/A'}`));
    } else {
      console.log(chalk.orange('⚠️ Departamento não encontrado'));
    }

    console.log(chalk.green('\n✅ TESTE CONCLUÍDO COM SUCESSO'));
    console.log(chalk.magenta('\n🎯 RESULTADOS IMPORTANTES:'));
    console.log(chalk.yellow('   - Conexão PostgreSQL: ✅ Funcionando'));
    console.log(chalk.yellow('   - Busca de usuários: ✅ Funcionando'));
    console.log(chalk.yellow('   - Relacionamentos: ✅ Funcionando'));
    console.log(chalk.yellow('   - Estrutura real mapeada: ✅ Pronta para uso'));

    console.log(chalk.cyan('\n📋 Próximos passos para integração completa:'));
    console.log('   1. ✅ PostgreSQL conectado e mapeado');
    console.log('   2. ⚠️ Resolver autenticação MongoDB');
    console.log('   3. 🔄 Integrar sistema de autenticação real');
    console.log('   4. 🔄 Atualizar rotas de chat para usar dados reais');
    console.log('   5. 🔄 Testar fluxo completo de login');

  } catch (error) {
    console.log(chalk.red('\n❌ ERRO NO TESTE:'));
    console.error(error);
  } finally {
    await postgresRepository.close();
  }
}

// Executar teste
testSimpleAuth().catch(console.error);