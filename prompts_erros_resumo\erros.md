>> PS D:\PROJETOS-BACKUP\pv_kiro> PS D:\PROJETOS-BACKUP\pv_kiro> node test-deepseek-api.js
>> 🚀 INICIANDO TESTES DA API DEEPSEEK
>> 
>> ==================================================
>> 🔐 Fazendo login...
>> ❌ Erro no login: connect ETIMEDOUT **************:3001
>> 
>> ❌ TESTE ABORTADO: Falha no login
>> PS D:\PROJETOS-BACKUP\pv_kiro> test-deepseek-api.js
Get-Process : Não é possível localizar um parâmetro posicional que aceite o argumento 'node'.
No linha:1 caractere:1
+ PS D:\PROJETOS-BACKUP\pv_kiro> node test-deepseek-api.js
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-Process], ParameterBindingException
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.GetProcessCommand
 
🚀 : O termo '🚀' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:2 caractere:1
+ 🚀 INICIANDO TESTES DA API DEEPSEEK
+ ~~
    + CategoryInfo          : ObjectNotFound: (🚀:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
================================================== : O termo '==================================================' não é reconhecido como nome de 
cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um caminho tiver sido incluído, veja se o caminho está 
correto e tente novamente.
No linha:4 caractere:1
+ ==================================================
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (===============...===============:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException
 
🔐 : O termo '🔐' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:5 caractere:1
+ 🔐 Fazendo login...
+ ~~
    + CategoryInfo          : ObjectNotFound: (🔐:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

❌ : O termo '❌' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:6 caractere:1
+ ❌ Erro no login: connect ETIMEDOUT **************:3001
+ ~
    + CategoryInfo          : ObjectNotFound: (❌:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

❌ : O termo '❌' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:8 caractere:1
+ ❌ TESTE ABORTADO: Falha no login
+ ~
    + CategoryInfo          : ObjectNotFound: (❌:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

Get-Process : Não é possível localizar um parâmetro posicional que aceite o argumento 'PS'.
No linha:9 caractere:1
+ PS D:\PROJETOS-BACKUP\pv_kiro> PS D:\PROJETOS-BACKUP\pv_kiro> node te ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-Process], ParameterBindingException
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.GetProcessCommand

🚀 : O termo '🚀' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:10 caractere:1
+ 🚀 INICIANDO TESTES DA API DEEPSEEK
+ ~~
    + CategoryInfo          : ObjectNotFound: (🚀:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

================================================== : O termo '==================================================' não é reconhecido como nome de 
cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um caminho tiver sido incluído, veja se o caminho está  
correto e tente novamente.
No linha:12 caractere:1
+ ==================================================
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (===============...===============:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

🔐 : O termo '🔐' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:13 caractere:1
+ 🔐 Fazendo login...
+ ~~
    + CategoryInfo          : ObjectNotFound: (🔐:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

❌ : O termo '❌' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:14 caractere:1
+ ❌ Erro no login: connect ETIMEDOUT **************:3001
+ ~
    + CategoryInfo          : ObjectNotFound: (❌:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

❌ : O termo '❌' não é reconhecido como nome de cmdlet, função, arquivo de script ou programa operável. Verifique a grafia do nome ou, se um 
caminho tiver sido incluído, veja se o caminho está correto e tente novamente.
No linha:16 caractere:1
+ ❌ TESTE ABORTADO: Falha no login
+ ~
    + CategoryInfo          : ObjectNotFound: (❌:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

Get-Process : Não é possível localizar um parâmetro posicional que aceite o argumento 'test-deepseek-api.js'.
No linha:17 caractere:1
+ PS D:\PROJETOS-BACKUP\pv_kiro> test-deepseek-api.js
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-Process], ParameterBindingException
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.GetProcessCommand

PS D:\PROJETOS-BACKUP\pv_kiro> 