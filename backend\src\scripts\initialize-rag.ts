#!/usr/bin/env tsx

import { config } from 'dotenv';
import { RAGDataPreparation } from '../services/ragDataPreparation';
import { RAGService } from '../services/ragService';
import chalk from 'chalk';

// Carregar variáveis de ambiente
config();

async function initializeRAG() {
  console.log(chalk.blue('🚀 Inicializando Sistema RAG - Prefeitura de Valparaíso'));
  console.log(chalk.gray('=' .repeat(60)));

  const startTime = Date.now();

  try {
    // Verificar variáveis essenciais
    console.log(chalk.yellow('📋 Verificando configurações...'));
    
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY não configurada no arquivo .env');
    }

    if (!process.env.RAG_ENABLED || process.env.RAG_ENABLED !== 'true') {
      throw new Error('RAG_ENABLED não está configurado como true no .env');
    }

    console.log(chalk.green('✅ Configurações verificadas'));

    // Passo 1: Testar conectividade
    console.log(chalk.yellow('\n🔌 Testando conectividade...'));
    
    const ragService = new RAGService();
    const connectionTest = await ragService.testConnection();
    
    if (!connectionTest) {
      throw new Error('Falha no teste de conectividade com OpenAI ou Chroma');
    }

    console.log(chalk.green('✅ Conectividade testada com sucesso'));

    // Passo 2: Preparar dados municipais
    console.log(chalk.yellow('\n📊 Preparando dados municipais...'));
    
    const dataPreparation = new RAGDataPreparation();
    const chunks = await dataPreparation.prepareAllData();

    if (chunks.length === 0) {
      throw new Error('Nenhum chunk de dados foi gerado. Verifique os arquivos de conhecimento.');
    }

    console.log(chalk.green(`✅ ${chunks.length} chunks de dados preparados`));

    // Mostrar estatísticas dos chunks
    const statsBySecretaria = chunks.reduce((acc, chunk) => {
      acc[chunk.metadata.secretaria] = (acc[chunk.metadata.secretaria] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(chalk.cyan('\n📈 Distribuição por Secretaria:'));
    Object.entries(statsBySecretaria).forEach(([secretaria, count]) => {
      console.log(chalk.cyan(`   ${secretaria}: ${count} chunks`));
    });

    const statsByTipo = chunks.reduce((acc, chunk) => {
      acc[chunk.metadata.tipo] = (acc[chunk.metadata.tipo] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log(chalk.cyan('\n📋 Distribuição por Tipo:'));
    Object.entries(statsByTipo).forEach(([tipo, count]) => {
      console.log(chalk.cyan(`   ${tipo}: ${count} chunks`));
    });

    // Passo 3: Criar base vetorial
    console.log(chalk.yellow('\n🗄️ Criando base vetorial...'));
    
    await ragService.initialize();
    
    // Verificar se collection já existe
    const collectionInfo = await ragService.getCollectionInfo();
    
    if (collectionInfo.exists && collectionInfo.count > 0) {
      console.log(chalk.yellow(`⚠️ Collection já existe com ${collectionInfo.count} documentos`));
      
      // Perguntar se deve recriar (em um script real, isso poderia ser um parâmetro)
      console.log(chalk.yellow('🔄 Recriando collection com novos dados...'));
      await ragService.createCollection();
    }

    // Passo 4: Vetorizar e armazenar documentos
    console.log(chalk.yellow('\n🧠 Vetorizando documentos (isso pode levar alguns minutos)...'));
    
    const embeddingStartTime = Date.now();
    await ragService.addDocuments(chunks);
    const embeddingTime = (Date.now() - embeddingStartTime) / 1000;

    console.log(chalk.green(`✅ Vetorização concluída em ${embeddingTime.toFixed(1)}s`));

    // Passo 5: Validar instalação
    console.log(chalk.yellow('\n🧪 Validando instalação...'));
    
    // Teste básico de busca
    const testQuery = 'Como solicitar alvará de construção?';
    console.log(chalk.gray(`   Teste: "${testQuery}"`));
    
    const testResults = await ragService.searchSimilar(testQuery, { topK: 2 });
    
    if (testResults.documents.length === 0) {
      throw new Error('Teste de busca falhou - nenhum documento encontrado');
    }

    console.log(chalk.green(`✅ Teste de busca: ${testResults.documents.length} documentos encontrados`));
    
    // Mostrar primeiro resultado como exemplo
    const firstResult = testResults.documents[0];
    console.log(chalk.cyan(`   Melhor resultado: ${firstResult.metadata.tipo} da ${firstResult.metadata.secretaria}`));
    console.log(chalk.gray(`   Relevância: ${(firstResult.metadata.relevancia * 100).toFixed(1)}%`));

    // Passo 6: Informações finais
    const totalTime = (Date.now() - startTime) / 1000;
    
    console.log(chalk.green('\n' + '='.repeat(60)));
    console.log(chalk.green('🎉 SISTEMA RAG INICIALIZADO COM SUCESSO!'));
    console.log(chalk.green('='.repeat(60)));
    
    console.log(chalk.white('\n📊 Resumo da Implementação:'));
    console.log(chalk.white(`   • Documentos vetorizados: ${chunks.length}`));
    console.log(chalk.white(`   • Secretarias cobertas: ${Object.keys(statsBySecretaria).length}`));
    console.log(chalk.white(`   • Tipos de conteúdo: ${Object.keys(statsByTipo).length}`));
    console.log(chalk.white(`   • Tempo total: ${totalTime.toFixed(1)}s`));
    console.log(chalk.white(`   • Tempo de vetorização: ${embeddingTime.toFixed(1)}s`));

    // Estimativa de custo
    const estimatedTokens = chunks.length * 100; // Estimativa média de tokens por chunk
    const estimatedCost = (estimatedTokens / 1000000) * 0.02; // $0.02 per 1M tokens
    console.log(chalk.white(`   • Custo estimado: $${estimatedCost.toFixed(4)}`));

    console.log(chalk.cyan('\n🔧 Próximos Passos:'));
    console.log(chalk.cyan('   1. O sistema RAG está pronto para uso'));
    console.log(chalk.cyan('   2. Execute testes com: npm run test-rag'));
    console.log(chalk.cyan('   3. Integre com deepSeekService.ts'));
    console.log(chalk.cyan('   4. Teste no frontend do chatbot'));

    console.log(chalk.green('\n✨ RAG System Ready! ✨\n'));

  } catch (error) {
    console.error(chalk.red('\n❌ ERRO NA INICIALIZAÇÃO DO RAG:'));
    console.error(chalk.red(`   ${error.message}`));
    
    if (error.stack) {
      console.error(chalk.gray('\n📋 Stack trace:'));
      console.error(chalk.gray(error.stack));
    }

    console.log(chalk.yellow('\n🔧 Possíveis soluções:'));
    console.log(chalk.yellow('   • Verificar se a chave OpenAI está correta'));
    console.log(chalk.yellow('   • Verificar se os arquivos de conhecimento existem'));
    console.log(chalk.yellow('   • Verificar conectividade com a internet'));
    console.log(chalk.yellow('   • Verificar permissões de escrita no diretório'));

    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  initializeRAG();
}

export { initializeRAG };