# Ícones do Sistema

Esta pasta contém os ícones utilizados no sistema de Chatbot Inteligente para Secretarias.

## Organização

Os ícones estão organizados nas seguintes categorias:

- `/navigation/` - Ícones para navegação (menu, setas, etc.)
- `/actions/` - Ícones para ações (salvar, editar, excluir, etc.)
- `/secretarias/` - Ícones específicos para cada secretaria
- `/status/` - Ícones de status (sucesso, erro, alerta, etc.)
- `/chat/` - Ícones relacionados à interface de chat

## Diretrizes para Ícones

1. Todos os ícones devem seguir o mesmo estilo visual
2. Use preferencialmente SVG para garantir escalabilidade
3. Mantenha consistência no tamanho e estilo dos traços
4. Nomeie os arquivos de forma descritiva usando kebab-case (ex: `user-profile.svg`)
5. Inclua versões em diferentes cores quando necessário (sufixos `-primary`, `-secondary`, etc.)

## Uso no Código

```jsx
import Image from "next/image";

// Exemplo de uso
<Image
  src="/images/icons/actions/edit.svg"
  alt="Editar"
  width={24}
  height={24}
/>;
```
