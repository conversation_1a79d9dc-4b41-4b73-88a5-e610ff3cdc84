#!/usr/bin/env tsx

import { config } from 'dotenv';
import { processMessage } from '../services/deepSeekService';
import { getRagService } from '../services/ragService';
import chalk from 'chalk';

// Carregar variáveis de ambiente
config();

interface TestCase {
  question: string;
  secretaria: string;
  expectedTopics: string[];
  description: string;
}

const testCases: TestCase[] = [
  {
    question: "Como solicitar alvará de construção?",
    secretaria: "obras",
    expectedTopics: ["alvará", "construção", "documentos", "procedimento"],
    description: "Teste de procedimento específico de obras"
  },
  {
    question: "Quais são os departamentos da área de saúde?",
    secretaria: "saude",
    expectedTopics: ["departamentos", "saúde", "UBS", "estrutura"],
    description: "Teste de estrutura organizacional da saúde"
  },
  {
    question: "Como fazer o cadastro no Cadastro Único?",
    secretaria: "assistencia_social",
    expectedTopics: ["cadastro único", "CRAS", "documentos", "benefícios"],
    description: "Teste de serviço social específico"
  },
  {
    question: "Quais documentos preciso para matrícula escolar?",
    secretaria: "educacao",
    expectedTopics: ["matrícula", "documentos", "escola", "educação"],
    description: "Teste de procedimento educacional"
  },
  {
    question: "Como solicitar poda de árvore?",
    secretaria: "meio_ambiente",
    expectedTopics: ["poda", "árvore", "meio ambiente", "licenciamento"],
    description: "Teste de serviço ambiental"
  }
];

async function runRAGComparison() {
  console.log(chalk.blue('🧪 TESTE COMPARATIVO: RAG vs NÃO-RAG'));
  console.log(chalk.gray('=' .repeat(60)));

  // Verificar se RAG está disponível
  const ragService = getRagService();
  const ragReady = await ragService.testConnection();
  
  if (!ragReady) {
    console.log(chalk.red('❌ RAG não está disponível. Execute initialize-rag.ts primeiro.'));
    process.exit(1);
  }

  console.log(chalk.green('✅ RAG está disponível, iniciando testes...'));

  const results: any[] = [];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(chalk.yellow(`\n📋 Teste ${i + 1}/${testCases.length}: ${testCase.description}`));
    console.log(chalk.cyan(`❓ Pergunta: "${testCase.question}"`));
    console.log(chalk.cyan(`🏢 Secretaria: ${testCase.secretaria}`));

    try {
      // Teste COM RAG
      console.log(chalk.blue('\n🧠 Testando COM RAG...'));
      process.env.RAG_ENABLED = 'true';
      
      const startTimeWithRAG = Date.now();
      const resultWithRAG = await processMessage(testCase.question, {
        userId: 'test_user',
        secretaria: testCase.secretaria,
        history: []
      });
      const timeWithRAG = Date.now() - startTimeWithRAG;

      // Teste SEM RAG
      console.log(chalk.magenta('🤖 Testando SEM RAG...'));
      process.env.RAG_ENABLED = 'false';
      
      const startTimeWithoutRAG = Date.now();
      const resultWithoutRAG = await processMessage(testCase.question, {
        userId: 'test_user',
        secretaria: testCase.secretaria,
        history: []
      });
      const timeWithoutRAG = Date.now() - startTimeWithoutRAG;

      // Restaurar RAG
      process.env.RAG_ENABLED = 'true';

      // Análise dos resultados
      const ragAnalysis = analyzeResponse(resultWithRAG.content, testCase.expectedTopics);
      const noRagAnalysis = analyzeResponse(resultWithoutRAG.content, testCase.expectedTopics);

      const testResult = {
        testCase,
        withRAG: {
          content: resultWithRAG.content,
          time: timeWithRAG,
          tokens: resultWithRAG.tokens.total,
          cost: resultWithRAG.cost.total,
          ragInfo: resultWithRAG.rag,
          topicCoverage: ragAnalysis.coverage,
          specificity: ragAnalysis.specificity
        },
        withoutRAG: {
          content: resultWithoutRAG.content,
          time: timeWithoutRAG,
          tokens: resultWithoutRAG.tokens.total,
          cost: resultWithoutRAG.cost.total,
          topicCoverage: noRagAnalysis.coverage,
          specificity: noRagAnalysis.specificity
        }
      };

      results.push(testResult);

      // Mostrar resultados do teste atual
      console.log(chalk.green('\n📊 RESULTADOS DO TESTE:'));
      
      console.log(chalk.blue('\n🧠 COM RAG:'));
      console.log(chalk.white(`   ⏱️ Tempo: ${timeWithRAG}ms`));
      console.log(chalk.white(`   🎯 Cobertura de tópicos: ${(ragAnalysis.coverage * 100).toFixed(1)}%`));
      console.log(chalk.white(`   📋 Especificidade: ${(ragAnalysis.specificity * 100).toFixed(1)}%`));
      console.log(chalk.white(`   📄 Docs encontrados: ${resultWithRAG.rag?.documentsFound || 0}`));
      console.log(chalk.white(`   💰 Custo: $${resultWithRAG.cost.total.toFixed(4)}`));

      console.log(chalk.magenta('\n🤖 SEM RAG:'));
      console.log(chalk.white(`   ⏱️ Tempo: ${timeWithoutRAG}ms`));
      console.log(chalk.white(`   🎯 Cobertura de tópicos: ${(noRagAnalysis.coverage * 100).toFixed(1)}%`));
      console.log(chalk.white(`   📋 Especificidade: ${(noRagAnalysis.specificity * 100).toFixed(1)}%`));
      console.log(chalk.white(`   💰 Custo: $${resultWithoutRAG.cost.total.toFixed(4)}`));

      // Comparação
      const improvementCoverage = ((ragAnalysis.coverage - noRagAnalysis.coverage) * 100);
      const improvementSpecificity = ((ragAnalysis.specificity - noRagAnalysis.specificity) * 100);
      
      console.log(chalk.green('\n📈 MELHORIA COM RAG:'));
      console.log(chalk.white(`   🎯 Cobertura: ${improvementCoverage > 0 ? '+' : ''}${improvementCoverage.toFixed(1)}%`));
      console.log(chalk.white(`   📋 Especificidade: ${improvementSpecificity > 0 ? '+' : ''}${improvementSpecificity.toFixed(1)}%`));

    } catch (error) {
      console.error(chalk.red(`❌ Erro no teste ${i + 1}:`, error.message));
      results.push({
        testCase,
        error: error.message
      });
    }
  }

  // Relatório final
  console.log(chalk.blue('\n' + '='.repeat(60)));
  console.log(chalk.blue('📋 RELATÓRIO FINAL DA COMPARAÇÃO'));
  console.log(chalk.blue('='.repeat(60)));

  const validResults = results.filter(r => !r.error);
  
  if (validResults.length === 0) {
    console.log(chalk.red('❌ Nenhum teste foi executado com sucesso'));
    return;
  }

  // Estatísticas gerais
  const avgImprovementCoverage = validResults.reduce((acc, r) => {
    return acc + (r.withRAG.topicCoverage - r.withoutRAG.topicCoverage);
  }, 0) / validResults.length;

  const avgImprovementSpecificity = validResults.reduce((acc, r) => {
    return acc + (r.withRAG.specificity - r.withoutRAG.specificity);
  }, 0) / validResults.length;

  const avgTimeWithRAG = validResults.reduce((acc, r) => acc + r.withRAG.time, 0) / validResults.length;
  const avgTimeWithoutRAG = validResults.reduce((acc, r) => acc + r.withoutRAG.time, 0) / validResults.length;

  const totalDocsFound = validResults.reduce((acc, r) => acc + (r.withRAG.ragInfo?.documentsFound || 0), 0);

  console.log(chalk.green('\n🎯 MÉTRICAS GERAIS:'));
  console.log(chalk.white(`   📊 Testes executados: ${validResults.length}/${testCases.length}`));
  console.log(chalk.white(`   📄 Total de documentos encontrados: ${totalDocsFound}`));
  console.log(chalk.white(`   ⏱️ Tempo médio com RAG: ${avgTimeWithRAG.toFixed(0)}ms`));
  console.log(chalk.white(`   ⏱️ Tempo médio sem RAG: ${avgTimeWithoutRAG.toFixed(0)}ms`));

  console.log(chalk.green('\n📈 MELHORIA MÉDIA COM RAG:'));
  console.log(chalk.white(`   🎯 Cobertura de tópicos: ${avgImprovementCoverage > 0 ? '+' : ''}${(avgImprovementCoverage * 100).toFixed(1)}%`));
  console.log(chalk.white(`   📋 Especificidade: ${avgImprovementSpecificity > 0 ? '+' : ''}${(avgImprovementSpecificity * 100).toFixed(1)}%`));

  // Conclusão
  console.log(chalk.green('\n🏆 CONCLUSÃO:'));
  if (avgImprovementCoverage > 0.1 && avgImprovementSpecificity > 0.1) {
    console.log(chalk.green('✅ RAG está melhorando significativamente a qualidade das respostas!'));
  } else if (avgImprovementCoverage > 0.05 || avgImprovementSpecificity > 0.05) {
    console.log(chalk.yellow('⚠️ RAG está melhorando as respostas, mas há espaço para otimização'));
  } else {
    console.log(chalk.red('❌ RAG não está apresentando melhorias significativas'));
  }

  console.log(chalk.blue('\n✨ Teste comparativo concluído! ✨\n'));
}

function analyzeResponse(content: string, expectedTopics: string[]): { coverage: number; specificity: number } {
  const contentLower = content.toLowerCase();
  
  // Calcular cobertura de tópicos esperados
  const coveredTopics = expectedTopics.filter(topic => 
    contentLower.includes(topic.toLowerCase())
  );
  const coverage = coveredTopics.length / expectedTopics.length;

  // Calcular especificidade (presença de termos específicos vs genéricos)
  const specificTerms = [
    'documento', 'formulário', 'procedimento', 'protocolo', 'prazo',
    'departamento', 'secretaria', 'endereço', 'horário', 'telefone',
    'alvará', 'licença', 'certidão', 'cadastro', 'solicitação'
  ];
  
  const specificTermsFound = specificTerms.filter(term => 
    contentLower.includes(term)
  ).length;
  
  const specificity = Math.min(specificTermsFound / 10, 1); // Normalizar para max 1

  return { coverage, specificity };
}

// Executar se chamado diretamente
if (require.main === module) {
  runRAGComparison();
}

export { runRAGComparison };