#!/usr/bin/env node

/**
 * TESTE ESPECÍFICO - CONEXÃO DEEPSEEK API
 * 
 * Testa diretamente a integração com DeepSeek V3
 * Verifica rate limiting, cache e processamento
 */

const axios = require('axios');

// Use localhost para execução no Windows
const BASE_URL = 'http://localhost:3001';

// Configuração do teste
const testConfig = {
  // Credenciais de teste com usuário real do PostgreSQL
  user: {
    login: '<EMAIL>', // IZADORA RABELO LIRA (usuário real)
    password: 'senha123', // Senha de teste (provavelmente não vai bater)
    secretaria: 'administracao'
  },
  // Mensagens de teste
  messages: [
    'Como solicitar alvará de funcionamento?',
    'Quais documentos preciso para licenciamento?',
    'Como solicitar alvará de funcionamento?' // Repetida para testar cache
  ]
};

let authToken = '';

async function login() {
  console.log('🔐 Fazendo login...');
  
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, testConfig.user);
    
    if (response.data.success && response.data.token) {
      authToken = response.data.token;
      console.log('✅ Login realizado com sucesso');
      console.log(`👤 Usuário: ${response.data.user.name} (${response.data.user.role})`);
      return true;
    } else {
      console.log('❌ Falha no login:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Erro no login:', error.response?.data || error.message);
    return false;
  }
}

async function testDeepSeekMessage(message, testNumber) {
  console.log(`\n📤 TESTE ${testNumber}: Enviando mensagem para DeepSeek...`);
  console.log(`💬 Mensagem: "${message}"`);
  
  const startTime = Date.now();
  
  try {
    const response = await axios.post(
      `${BASE_URL}/api/chat/message`,
      {
        message: message,
        userId: 'test-user-1',
        secretaria: testConfig.user.secretaria
      },
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    if (response.data.success) {
      console.log('✅ Resposta recebida da DeepSeek API');
      console.log(`⚡ Tempo de resposta: ${responseTime}ms`);
      
      // Verificar estrutura da resposta
      const responseText = response.data.data?.response || response.data.response || 'Resposta não encontrada';
      const cost = response.data.data?.cost || response.data.cost || 0;
      const cacheHit = response.data.cacheHit || false;
      
      console.log(`💰 Custo: $${cost || 'N/A'}`);
      console.log(`📊 Cache: ${cacheHit ? 'HIT' : 'MISS'}`);
      console.log(`🎯 Resposta: ${responseText.substring(0, 100)}...`);
      
      // Detalhes técnicos
      if (response.data.metadata) {
        console.log(`📈 Tokens usados: ${response.data.metadata.tokens?.total || 'N/A'}`);
        console.log(`⏱️  Processamento: ${response.data.metadata.processingTime}ms`);
      }
      
      return {
        success: true,
        responseTime,
        cacheHit,
        cost,
        tokensUsed: response.data.metadata?.tokens?.total
      };
    } else {
      console.log('❌ Falha na resposta:', response.data);
      return { success: false, error: response.data };
    }
    
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    console.log('❌ Erro na requisição:');
    console.log(`⚡ Tempo até erro: ${responseTime}ms`);
    
    if (error.response) {
      console.log(`📄 Status: ${error.response.status}`);
      console.log(`📄 Dados: ${JSON.stringify(error.response.data, null, 2)}`);
    } else {
      console.log(`📄 Erro: ${error.message}`);
    }
    
    return { success: false, error: error.response?.data || error.message };
  }
}

async function testRateLimit() {
  console.log('\n🚦 Testando Rate Limiting...');
  
  try {
    const response = await axios.get(
      `${BASE_URL}/api/chat/metrics`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      }
    );
    
    if (response.data.success && response.data.rateLimits) {
      const limits = response.data.rateLimits;
      console.log('✅ Rate limits ativos:');
      console.log(`📊 Mensagens usadas hoje: ${limits.messagesUsed || 0}/${limits.messagesLimit || 'N/A'}`);
      console.log(`🎫 Tokens usados hoje: ${limits.tokensUsed || 0}/${limits.tokensLimit || 'N/A'}`);
      console.log(`⏰ Reset em: ${limits.resetTime ? new Date(limits.resetTime).toLocaleString() : 'N/A'}`);
    } else {
      console.log('⚠️ Rate limits não disponíveis no momento');
    }
  } catch (error) {
    console.log('❌ Erro ao verificar rate limits:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 INICIANDO TESTES DA API DEEPSEEK\n');
  console.log('=' .repeat(50));
  
  // 1. Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ TESTE ABORTADO: Falha no login');
    return;
  }
  
  // 2. Verificar rate limits iniciais
  await testRateLimit();
  
  // 3. Testar mensagens
  const results = [];
  
  for (let i = 0; i < testConfig.messages.length; i++) {
    const result = await testDeepSeekMessage(testConfig.messages[i], i + 1);
    results.push(result);
    
    // Pausa entre testes
    if (i < testConfig.messages.length - 1) {
      console.log('\n⏳ Aguardando 2 segundos...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 4. Verificar rate limits finais
  await testRateLimit();
  
  // 5. Resumo final
  console.log('\n' + '=' .repeat(50));
  console.log('📋 RESUMO DOS TESTES');
  console.log('=' .repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const cacheHits = results.filter(r => r.cacheHit).length;
  const totalCost = results.reduce((sum, r) => sum + (parseFloat(r.cost) || 0), 0);
  
  console.log(`✅ Testes bem-sucedidos: ${successCount}/${results.length}`);
  console.log(`📊 Cache hits: ${cacheHits}/${results.length}`);
  console.log(`💰 Custo total: $${totalCost.toFixed(6)}`);
  
  if (successCount === results.length) {
    console.log('\n🎉 TODOS OS TESTES PASSARAM!');
    console.log('✅ DeepSeek API está funcionando perfeitamente');
    console.log('✅ Rate limiting está ativo');
    console.log('✅ Cache está funcionando');
    console.log('✅ Processamento é imediato');
  } else {
    console.log('\n⚠️  ALGUNS TESTES FALHARAM');
    console.log('❌ Verifique os logs acima para detalhes');
  }
  
  console.log('\n🔚 Teste finalizado');
}

// Executar testes
runTests().catch(error => {
  console.log('\n💥 ERRO CRÍTICO:', error.message);
  process.exit(1);
});