const axios = require('axios');

const API_BASE = 'http://localhost:3001';

async function testCommonPasswords() {
  console.log('=== TESTE DE SENHAS COMUNS PARA OTTO ===\n');
  
  // Lista de senhas comuns para testar
  const commonPasswords = [
    '123456',
    'alfa5DELTA', 
    'otto',
    '<PERSON>',
    'OTTO',
    'otto123',
    'Otto123',
    'OTTO123',
    '123',
    'admin',
    'admin123',
    'prefeitura',
    'valparaiso',
    'senha',
    'senha123',
    '12345',
    '123456789',
    'otto@123',
    'otto2024',
    'otto2025'
  ];
  
  console.log(`Testando ${commonPasswords.length} senhas possíveis...\n`);
  
  let successCount = 0;
  
  for (let i = 0; i < commonPasswords.length; i++) {
    const password = commonPasswords[i];
    
    try {
      console.log(`${i + 1}/${commonPasswords.length}. Testando: "${password}"`);
      
      const loginData = {
        login: '<EMAIL>',
        password: password,
        secretaria: 'administracao'
      };
      
      const loginResponse = await axios.post(`${API_BASE}/api/auth/login`, loginData);
      
      // Se chegou aqui, o login funcionou!
      console.log(`\n🎉 SENHA ENCONTRADA: "${password}"`);
      console.log(`✅ Login bem-sucedido!`);
      console.log(`Usuário: ${loginResponse.data.user.name}`);
      console.log(`Token: ${loginResponse.data.token.substring(0, 50)}...`);
      
      successCount++;
      break; // Parar no primeiro sucesso
      
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`   ❌ Senha incorreta`);
      } else {
        console.log(`   ❌ Erro: ${error.response?.data?.error || error.message}`);
      }
    }
    
    // Pequena pausa para não sobrecarregar o servidor
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  if (successCount === 0) {
    console.log(`\n❌ Nenhuma das ${commonPasswords.length} senhas testadas funcionou.`);
    console.log('\n🔍 Sugestões:');
    console.log('1. Verificar se existe outro usuário "otto" no sistema');
    console.log('2. Confirmar as credenciais corretas com o administrador');
    console.log('3. Verificar se a conta não está bloqueada ou inativa');
    console.log('4. Testar com outro usuário do sistema');
  }
}

// Executar teste
testCommonPasswords();