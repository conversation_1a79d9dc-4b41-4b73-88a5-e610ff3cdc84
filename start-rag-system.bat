@echo off
echo 🚀 Iniciando Sistema RAG Completo - Prefeitura de Valparaiso

echo.
echo 📋 Verificando dependencias...

REM Verificar se Docker está instalado
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker não encontrado. Instalando via Python...
    echo.
    echo 🐍 Verificando Python...
    python --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python não encontrado. Por favor instale Python 3.8+ ou Docker
        echo.
        echo 💡 Opções:
        echo    1. Instalar Docker: https://docs.docker.com/desktop/install/windows-install/
        echo    2. Instalar Python: https://www.python.org/downloads/
        pause
        exit /b 1
    )
    
    echo ✅ Python encontrado, instalando ChromaDB...
    pip install chromadb uvicorn
    
    echo.
    echo 🌐 Iniciando servidor ChromaDB via Python...
    start "ChromaDB Server" python start-chroma-server.py
    timeout /t 5 /nobreak >nul
) else (
    echo ✅ Docker encontrado, usando container ChromaDB...
    
    echo.
    echo 🐳 Iniciando container ChromaDB...
    docker-compose up -d chromadb
    
    echo.
    echo ⏳ Aguardando ChromaDB inicializar...
    timeout /t 10 /nobreak >nul
)

echo.
echo 🧪 Testando conectividade ChromaDB...
curl -s http://localhost:8000/api/v1/heartbeat >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ChromaDB não está respondendo em http://localhost:8000
    echo 🔧 Verifique se o servidor iniciou corretamente
    pause
    exit /b 1
)

echo ✅ ChromaDB está rodando em http://localhost:8000

echo.
echo 🔄 Inicializando base vetorial...
cd backend
npx tsx src/scripts/initialize-rag.ts

if %errorlevel% neq 0 (
    echo.
    echo ❌ Erro na inicialização do RAG
    pause
    exit /b 1
)

echo.
echo 🎉 SISTEMA RAG PRONTO!
echo.
echo 📋 Serviços ativos:
echo    • ChromaDB: http://localhost:8000
echo    • Backend: http://localhost:3001 (quando executar npm run dev)
echo.
echo 🚀 Para iniciar o backend:
echo    cd backend ^&^& npm run dev
echo.
echo 🧪 Para testar o RAG:
echo    cd backend ^&^& npx tsx src/scripts/test-rag-comparison.ts
echo.

pause