[{"id": "dept_282", "content": "Departamento: ABRIGAMENTO INFANTIL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["abrigamento", "infantil"], "fonte": "postgresql_departments"}}, {"id": "dept_186", "content": "Departamento: ACCPAS - ASSOCIAÇÃO CRECHE COMUNITÁRIA PARAÍSO DOS SONHOS (ESCOLA CONVENIADA)\nTipo: UNIDADE_ESCOLAR\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["accpas", "associa", "creche", "comunit", "ria", "sonhos", "escola", "conveniada"], "fonte": "postgresql_departments"}}, {"id": "dept_510", "content": "Departamento: ACESSORIA DE GABINETE - INFRAESTRUTURA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "obras", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["acessoria", "gabinete", "infraestrutura"], "fonte": "postgresql_departments"}}, {"id": "dept_313", "content": "Departamento: ACIVALGO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["<PERSON><PERSON><PERSON><PERSON>"], "fonte": "postgresql_departments"}}, {"id": "dept_491", "content": "Departamento: ADMINISTRAÇÃO REGIONAL DO CÉU AZUL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["administra", "regional", "azul"], "fonte": "postgresql_departments"}}, {"id": "dept_97", "content": "Departamento: ADMINISTRATIVO - FISCALIZAÇÃO DE POSTURAS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["administrativo", "fiscaliza", "posturas"], "fonte": "postgresql_departments"}}, {"id": "dept_89", "content": "Departamento: ADMINISTRATIVO - INFRAESTRUTURA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "obras", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["administrativo", "infraestrutura"], "fonte": "postgresql_departments"}}, {"id": "dept_451", "content": "Departamento: ADM POSTURA - SUSFIM\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["adm", "postura", "susfim"], "fonte": "postgresql_departments"}}, {"id": "dept_117", "content": "Departamento: AGÊNCIA MUNICIPAL DE SEGURANÇA E GUARDA MUNICIPAL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["ncia", "municipal", "seguran", "guarda", "municipal"], "fonte": "postgresql_departments"}}, {"id": "dept_103", "content": "Departamento: AGÊNCIA MUNICIPAL DE TRÂNSITO E TRANSPORTE\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["ncia", "municipal", "nsito", "transporte"], "fonte": "postgresql_departments"}}, {"id": "dept_45", "content": "Departamento: ALMOXARIFADO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["almoxarifado"], "fonte": "postgresql_departments"}}, {"id": "dept_74", "content": "Departamento: AMBULATÓRIO - CAIS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "saude", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["ambulat", "rio", "cais"], "fonte": "postgresql_departments"}}, {"id": "dept_377", "content": "Departamento: ANÁLISES CLÍNICAS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["lises", "nicas"], "fonte": "postgresql_departments"}}, {"id": "dept_85", "content": "Departamento: APOIO ADMINISTRATIVO MEIO AMBIENTE\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "meio_ambiente", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["apoio", "administrativo", "meio", "ambiente"], "fonte": "postgresql_departments"}}, {"id": "dept_507", "content": "Departamento: ARQUIVO COMPRAS - EDUCAÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["arquivo", "compras", "educa"], "fonte": "postgresql_departments"}}, {"id": "dept_456", "content": "Departamento: <PERSON><PERSON><PERSON><PERSON> RECURSOS HUMANOS- EDUCAÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["arquivo", "recursos", "humanos", "educa"], "fonte": "postgresql_departments"}}, {"id": "dept_447", "content": "Departamento: ARQUIVO-RH I SECRETARIA DE ADMINISTRAÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["arquivo", "secretaria", "administra"], "fonte": "postgresql_departments"}}, {"id": "dept_435", "content": "Departamento: ASSESORIA JURÍDICA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assesoria", "jur", "dica"], "fonte": "postgresql_departments"}}, {"id": "dept_316", "content": "Departamento: ASSESSORIA CERIMONIAL E EVENTOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cerimonial", "eventos"], "fonte": "postgresql_departments"}}, {"id": "dept_47", "content": "Departamento: ASSESSORIA DE COMUNICAÇÃO SOCIAL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "assistencia_social", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "comunica", "social"], "fonte": "postgresql_departments"}}, {"id": "dept_287", "content": "Departamento: ASSESSORIA DE GABINETE\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "gabinete"], "fonte": "postgresql_departments"}}, {"id": "dept_121", "content": "Departamento: ASSESSORIA DE GESTÃO FINANCEIRA DOS FUNDOS E RECURSOS DA EDUCAÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "gest", "financeira", "fundos", "recursos", "educa"], "fonte": "postgresql_departments"}}, {"id": "dept_427", "content": "Departamento: ASSESSORIA DE PLANEJAMENTO E INSPETORIA ESCOLAR\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "planejamento", "inspetoria", "escolar"], "fonte": "postgresql_departments"}}, {"id": "dept_261", "content": "Departamento: ASSESSORIA DE PLANEJAMENTO E PROJETOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "planejamento", "projetos"], "fonte": "postgresql_departments"}}, {"id": "dept_123", "content": "Departamento: ASSESSORIA DE PLANEJAMENTO, PROJETOS E INSPEÇÃO ESCOLAR\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "planejamento", "projetos", "inspe", "escolar"], "fonte": "postgresql_departments"}}, {"id": "dept_349", "content": "Departamento: Assessoria de Planejamentos. Projetos e Inspeção Escolar\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "educacao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "planejamentos", "projetos", "inspe", "escolar"], "fonte": "postgresql_departments"}}, {"id": "dept_264", "content": "Departamento: ASSESSORIA ESPECIAL DA MULHER\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "especial", "mulher"], "fonte": "postgresql_departments"}}, {"id": "dept_300", "content": "Departamento: ASSESSORIA ESPECIAL DE PROJETOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "especial", "projetos"], "fonte": "postgresql_departments"}}, {"id": "dept_309", "content": "Departamento: ASSESSORIA EXECUTIVA DE INVESTIMENTOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "executiva", "investimentos"], "fonte": "postgresql_departments"}}, {"id": "dept_437", "content": "Departamento: ASSESSORIA EXECUTIVA DE PROJETOS VIGILÂNCIA SANITÁRIA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "executiva", "projetos", "vigil", "ncia", "sanit", "ria"], "fonte": "postgresql_departments"}}, {"id": "dept_262", "content": "Departamento: ASSESSORIA FINANCEIRA DO FMAS E FMDCA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "financeira", "fmas", "fmdca"], "fonte": "postgresql_departments"}}, {"id": "dept_399", "content": "Departamento: ASSESSORIA JURÍDICA | IPASVAL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "jur", "dica", "<PERSON><PERSON><PERSON>"], "fonte": "postgresql_departments"}}, {"id": "dept_493", "content": "Departamento: ASSESSORIA TÉCNICA ADMINISTRATIVA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnica", "administrativa"], "fonte": "postgresql_departments"}}, {"id": "dept_459", "content": "Departamento: ASSESSORIA TÉCNICA ADMINISTRATIVA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnica", "administrativa"], "fonte": "postgresql_departments"}}, {"id": "dept_263", "content": "Departamento: ASSESSORIA TÉCNICA ADMINISTRATIVA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnica", "administrativa"], "fonte": "postgresql_departments"}}, {"id": "dept_308", "content": "Departamento: ASSESSORIA TÉCNICA ADMINISTRATIVA DO PROCON\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnica", "administrativa", "procon"], "fonte": "postgresql_departments"}}, {"id": "dept_480", "content": "Departamento: ASSESSORIA TÉCNICA VIGILÂNCIA SANITÁRIA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnica", "vigil", "ncia", "sanit", "ria"], "fonte": "postgresql_departments"}}, {"id": "dept_352", "content": "Departamento: Assessoria Técnico Administrativa\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["assessoria", "cnico", "administrativa"], "fonte": "postgresql_departments"}}, {"id": "dept_365", "content": "Departamento: ATENDE VAL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["atende", "val"], "fonte": "postgresql_departments"}}, {"id": "dept_94", "content": "Departamento: ATENDIMENTO INICIAL - CENTRO DE REFERÊNCIA EM DIABETES E HIPERTENSÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["atendimento", "inicial", "centro", "refer", "ncia", "diabetes", "hipertens"], "fonte": "postgresql_departments"}}, {"id": "dept_359", "content": "Departamento: ATENDIMENTO INICIAL - HOSPITAL DOS OLHOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["atendimento", "inicial", "hospital", "olhos"], "fonte": "postgresql_departments"}}, {"id": "dept_443", "content": "Departamento: ATENDIMENTO PROCON\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["atendimento", "procon"], "fonte": "postgresql_departments"}}, {"id": "dept_87", "content": "Departamento: ATENDIMENTO VALPARAÍSO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["atendimento", "valpara"], "fonte": "postgresql_departments"}}, {"id": "dept_374", "content": "Departamento: AUDIO VISUAL\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["audio", "visual"], "fonte": "postgresql_departments"}}, {"id": "dept_102", "content": "Departamento: AUTO DE INFRAÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["auto", "infra"], "fonte": "postgresql_departments"}}, {"id": "dept_56", "content": "Departamento: AUXILIAR LEGISLATIVO/MANUTENÇÃO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["auxiliar", "legislativo", "manuten"], "fonte": "postgresql_departments"}}, {"id": "dept_280", "content": "Departamento: BANCO DE ALIMENTOS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["banco", "alimentos"], "fonte": "postgresql_departments"}}, {"id": "dept_304", "content": "Departamento: BIBLIOTECA CORA CORALINA\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["biblioteca", "cora", "coral<PERSON>"], "fonte": "postgresql_departments"}}, {"id": "dept_106", "content": "Departamento: CADASTRO DOS CONTRATOS DE OBRAS\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "obras", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["cadastro", "contratos", "obras"], "fonte": "postgresql_departments"}}, {"id": "dept_485", "content": "Departamento: CADASTRO IMOBILIÁRIO\nTipo: N\nStatus: Ativo", "metadata": {"secretaria": "administracao", "tipo": "departamento", "nivel_acesso": "publico", "relevancia": 0.5, "keywords": ["cadastro", "imobili", "rio"], "fonte": "postgresql_departments"}}, {"id": "service_4", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - SOS\nCobra Taxa: Não\nDepartamento ID: 26", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "sos"], "fonte": "postgresql_services"}}, {"id": "service_5", "content": "Serviço: CREDENCIAMENTO\nCobra Taxa: Não\nDepartamento ID: 22", "metadata": {"secretaria": "meio_ambiente", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["credenciamento"], "fonte": "postgresql_services"}}, {"id": "service_7", "content": "Serviço: ALVARÁ DE FUNCIONAMENTO\nCobra Taxa: Não\nDepartamento ID: 24", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["<PERSON><PERSON>", "funcionamento"], "fonte": "postgresql_services"}}, {"id": "service_8", "content": "Serviço: FISCALIZAÇÃO DE POSTURAS\nCobra Taxa: Não\nDepartamento ID: 28\nModelo Abertura: 66\nModelo Despacho: 66", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.8999999999999999, "keywords": ["fiscaliza", "posturas"], "fonte": "postgresql_services"}}, {"id": "service_9", "content": "Serviço: ANÁLISE DO PROCESSO\nCobra Taxa: Não\nDepartamento ID: 29", "metadata": {"secretaria": "administracao", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["lise", "processo"], "fonte": "postgresql_services"}}, {"id": "service_10", "content": "Serviço: VIGILÂNCIA SANITÁRIA\nCobra Taxa: Não\nDepartamento ID: 30", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["vigil", "ncia", "sanit", "ria"], "fonte": "postgresql_services"}}, {"id": "service_11", "content": "Serviço: EMISSÃO DO ALVARÁ\nCobra Taxa: Não\nDepartamento ID: 24", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["emiss", "<PERSON><PERSON>"], "fonte": "postgresql_services"}}, {"id": "service_12", "content": "Serviço: EMISSÃO TAXA ALVARÁ\nCobra Taxa: Não\nDepartamento ID: 39", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["emiss", "taxa", "<PERSON><PERSON>"], "fonte": "postgresql_services"}}, {"id": "service_13", "content": "Serviço: CERTIDÕES\nCobra Taxa: Não\nDepartamento ID: 36", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["certid"], "fonte": "postgresql_services"}}, {"id": "service_14", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - TAPA BURACO\nCobra Taxa: Não\nDepartamento ID: 31", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "tapa", "buraco"], "fonte": "postgresql_services"}}, {"id": "service_15", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - BOCA DE LOBO\nCobra Taxa: Não\nDepartamento ID: 32", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "boca", "lobo"], "fonte": "postgresql_services"}}, {"id": "service_16", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - ROÇAGEM\nCobra Taxa: Não\nDepartamento ID: 33", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "agem"], "fonte": "postgresql_services"}}, {"id": "service_17", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - ILUMINAÇÃO PÚBLICA\nCobra Taxa: Não\nDepartamento ID: 34", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "ilumina", "blica"], "fonte": "postgresql_services"}}, {"id": "service_18", "content": "Serviço: DIVISÃO DE SERVIÇOS GERAIS - RETIRADA DE ENTULHO\nCobra Taxa: Não\nDepartamento ID: 35", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["divis", "servi", "gerais", "retirada", "entu<PERSON><PERSON>"], "fonte": "postgresql_services"}}, {"id": "service_19", "content": "Serviço: EMISSÃO DE CERTIFICADO\nCobra Taxa: Não\nDepartamento ID: 37", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["emiss", "certificado"], "fonte": "postgresql_services"}}, {"id": "service_20", "content": "Serviço: EMISSÃO DE CERTIFICADO\nCobra Taxa: Não\nDepartamento ID: 37", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["emiss", "certificado"], "fonte": "postgresql_services"}}, {"id": "service_21", "content": "Serviço: ADMISSÃO DE SERVIDOR\nCobra Taxa: Não\nDepartamento ID: 27", "metadata": {"secretaria": "geral", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["admiss", "servidor"], "fonte": "postgresql_services"}}, {"id": "service_22", "content": "Serviço: DENÚNCIA FISCALIZAÇÃO DE POSTURAS\nCobra Taxa: Não\nDepartamento ID: 97", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["den", "ncia", "fiscaliza", "posturas"], "fonte": "postgresql_services"}}, {"id": "service_23", "content": "Serviço: FISCALIZAÇÃO AMBIENTAL\nCobra Taxa: Não\nDepartamento ID: 42", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["fiscaliza", "ambiental"], "fonte": "postgresql_services"}}, {"id": "service_24", "content": "Serviço: FISCALIZAÇÃO TRÂNSITO E TRANSPORTE\nCobra Taxa: Não\nDepartamento ID: 43", "metadata": {"secretaria": "obras", "tipo": "servico", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["fiscaliza", "nsito", "transporte"], "fonte": "postgresql_services"}}, {"id": "form_198", "content": "Formulário: 232323\nDescrição: teset", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["232323"], "fonte": "postgresql_forms"}}, {"id": "form_403", "content": "Formulário: 2ª Edição das Olimpíadas Escolares", "metadata": {"secretaria": "educacao", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["edi", "olimp", "adas", "escolares"], "fonte": "postgresql_forms"}}, {"id": "form_458", "content": "Formulário: 7º CORRIDA DO MEIO AMBIENTE", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["corrida", "meio", "ambiente"], "fonte": "postgresql_forms"}}, {"id": "form_283", "content": "Formulário: Abertura de Chamado TI\nDescrição: Abertura de Chamado", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["abertura", "chamado"], "fonte": "postgresql_forms"}}, {"id": "form_373", "content": "Formulário: ADMISSÃO DOS COMISSIONADOS E EFETIVOS", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["admiss", "comissionados", "efetivos"], "fonte": "postgresql_forms"}}, {"id": "form_471", "content": "Formulário: ADMISSÃO DOS EFETIVOS", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["admiss", "efetivos"], "fonte": "postgresql_forms"}}, {"id": "form_392", "content": "Formulário: ADMISSÃO SERVIDORES CÂMARA", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["admiss", "servid<PERSON>", "mara"], "fonte": "postgresql_forms"}}, {"id": "form_192", "content": "Formulário: Agendamento de Vistoria", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["agendamento", "vistoria"], "fonte": "postgresql_forms"}}, {"id": "form_309", "content": "Formulário: Agendamento Laboratório", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["agendamento", "laborat", "rio"], "fonte": "postgresql_forms"}}, {"id": "form_400", "content": "Formulário: Agricultura", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["agricultura"], "fonte": "postgresql_forms"}}, {"id": "form_109", "content": "Formulário: ALMOXARIFADO", "metadata": {"secretaria": "geral", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["almoxarifado"], "fonte": "postgresql_forms"}}, {"id": "form_284", "content": "Formulário: Alvará de Autorização\nDescrição: Alvará de Autorização", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "autoriza"], "fonte": "postgresql_forms"}}, {"id": "form_285", "content": "Formulário: Alvará de Construção\nDescrição: Processo para análise de projeto arquitetônico, e emissão do alvará de construção do respectivo projeto aprovadoProcesso para análise de projeto arquitetônico, e emissão do alvará de construção do respectivo projeto aprovado.", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "constru"], "fonte": "postgresql_forms"}}, {"id": "form_286", "content": "Formulário: Alvará de Demolição\nDescrição: - Todos os arquivos devem ser assinados e escaneados.\n\n\n\nAnexe esta declaração assinada por todos os envolvidos.\nAcesse o link, vá em Arquivo, e clique em Fazer Download, e escolha (.docx)\n\nCONSULTA DAS LEGISLAÇÕES\n\nÉ importante que o requerente atente-se às Leis vigentes da União, do Estado e as Municipais seguintes:\n\n\nLei Complementar nº 067 (Código de Obras);\nLei Complementar nº 044 (Lei de parcelamento e Uso de Solo);\nLei Complementar nº 063 (Plano Diretor);\nLei Complementar nº 090 (que altera as Leis Complementar 044/2008 e 067/2012 e dá outras providências) e todas as respectivas alterações).", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "demoli"], "fonte": "postgresql_forms"}}, {"id": "form_108", "content": "Formulário: Alvará de Funcionamento", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "funcionamento"], "fonte": "postgresql_forms"}}, {"id": "form_61", "content": "Formulário: Alvará de Funcionamento - Pessoa Física", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "funcionamento", "pessoa", "sica"], "fonte": "postgresql_forms"}}, {"id": "form_62", "content": "Formulário: Alvará de Funcionamento - Pessoa Física Replicado apartir de ID 61", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "funcionamento", "pessoa", "sica", "replicado", "apartir"], "fonte": "postgresql_forms"}}, {"id": "form_257", "content": "Formulário: Alvará de Funcionamento Provisório", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "funcionamento", "provis", "rio"], "fonte": "postgresql_forms"}}, {"id": "form_247", "content": "Formulário: Alvará de Funcionamento Replicado apartir de ID 108", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "funcionamento", "replicado", "apartir", "108"], "fonte": "postgresql_forms"}}, {"id": "form_191", "content": "Formulário: ALVARÁ DE LICENÇA SANITÁRIA", "metadata": {"secretaria": "obras", "tipo": "formulario", "nivel_acesso": "publico", "relevancia": 0.8, "keywords": ["<PERSON><PERSON>", "licen", "sanit", "ria"], "fonte": "postgresql_forms"}}, {"id": "stats_usuarios", "content": "Estatísticas de usuarios:\ntotal_users: 111396\nservidores: 1513\ncontas_ativas: 44607\ncom_email: 49105", "metadata": {"secretaria": "administracao", "tipo": "estatistica", "nivel_acesso": "servidor", "relevancia": 0.6, "keywords": ["usuarios", "estatisticas", "dados", "municipal"], "fonte": "postgresql_statistics"}}, {"id": "text_0", "content": "tabela: servicos", "metadata": {"secretaria": "geral", "tipo": "procedimento", "nivel_acesso": "publico", "relevancia": 0.7, "keywords": ["tabela", "servic<PERSON>", "registros", "amostra", "descricao", "atendimento", "inicial", "hmv", "cobrar_taxa", "false"], "fonte": "municipal_knowledge"}}]