import { Router } from 'express';
import ChatController from '../controllers/chatController';
import { chatCacheMiddleware } from '../middleware/cacheMiddleware';

const router = Router();

/**
 * @route POST /api/chat/message
 * @desc Processa mensagem do usuário com sistema inteligente de cache
 * @access Private (requer autenticação)
 * 
 * Body:
 * {
 *   "message": "Quantos funcionários tem na secretaria de saúde?",
 *   "userId": "user123",
 *   "secretaria": "saude",
 *   "conversationId": "conv456",
 *   "forceImmediate": false
 * }
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "response": "A Secretaria de Saúde possui 45 funcionários...",
 *     "source": "cache|api|queued",
 *     "cacheType": "exact|semantic",
 *     "cost": 0.00219,
 *     "timestamp": "2025-01-23T10:30:00Z",
 *     "queueInfo": {
 *       "messageId": "msg_123",
 *       "estimatedProcessTime": "2025-01-23T13:30:00Z",
 *       "estimatedSavings": 0.001095
 *     }
 *   },
 *   "metadata": {
 *     "urgency": "normal",
 *     "discountActive": false,
 *     "nextDiscountTime": "2025-01-23T13:30:00Z",
 *     "processingTime": 1250
 *   }
 * }
 */
router.post('/message', chatCacheMiddleware, ChatController.processMessage);

/**
 * @route GET /api/chat/queue/:userId
 * @desc Obter status das filas do usuário
 * @access Private
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "userMessages": [
 *       {
 *         "id": "msg_123",
 *         "message": "Relatório de gastos...",
 *         "urgency": "batch",
 *         "queuedAt": "2025-01-23T10:00:00Z",
 *         "estimatedProcessTime": "2025-01-23T13:30:00Z",
 *         "estimatedSavings": 0.001095
 *       }
 *     ],
 *     "globalStatus": {
 *       "totalQueued": 15,
 *       "processingNow": 3,
 *       "nextDiscountTime": "2025-01-23T13:30:00Z",
 *       "isDiscountActive": false,
 *       "estimatedWaitTime": 180
 *     },
 *     "discountInfo": {
 *       "isActive": false,
 *       "nextTime": "2025-01-23T13:30:00Z",
 *       "savings": "50% de economia no horário de desconto"
 *     }
 *   }
 * }
 */
router.get('/queue/:userId', ChatController.getUserQueueStatus);

/**
 * @route POST /api/chat/queue/cancel
 * @desc Cancelar mensagem na fila
 * @access Private
 * 
 * Body:
 * {
 *   "messageId": "msg_123",
 *   "userId": "user123"
 * }
 */
router.post('/queue/cancel', ChatController.cancelQueuedMessage);

/**
 * @route POST /api/chat/queue/promote
 * @desc Promover mensagem para processamento imediato
 * @access Private
 * 
 * Body:
 * {
 *   "messageId": "msg_123",
 *   "userId": "user123"
 * }
 */
router.post('/queue/promote', ChatController.promoteMessage);

/**
 * @route GET /api/chat/metrics
 * @desc Obter métricas de cache e economia
 * @access Private (Admin)
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "cache": {
 *       "totalRequests": 1000,
 *       "cacheHits": 750,
 *       "cacheMisses": 250,
 *       "totalSavings": 125.50,
 *       "hitRate": 75.0
 *     },
 *     "queue": {
 *       "totalProcessed": 500,
 *       "totalSavings": 65.75,
 *       "avgProcessingTime": 2.5,
 *       "discountUsageRate": 45.0
 *     },
 *     "discountInfo": {
 *       "isActive": false,
 *       "nextTime": "2025-01-23T13:30:00Z",
 *       "potentialSavings": "Até 65% de economia com cache + desconto"
 *     },
 *     "realTimeCost": {
 *       "perMessage": "$0.00219",
 *       "dailyBudget": "$50.00",
 *       "monthlyEstimate": "$32.85"
 *     }
 *   }
 * }
 */
router.get('/metrics', ChatController.getCacheMetrics);

/**
 * @route GET /api/chat/cost-report
 * @desc Obter relatório detalhado de custos
 * @access Private (Admin)
 * 
 * Query params:
 * - period: 'daily' | 'monthly' (default: 'daily')
 * - date: YYYY-MM-DD format (default: today)
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "period": "2025-01-23",
 *     "totalCost": 15.75,
 *     "totalSavings": 35.25,
 *     "messagesProcessed": 450,
 *     "cacheHitRate": 68.5,
 *     "averageCostPerMessage": 0.035,
 *     "bySecretaria": {
 *       "saude": 5.25,
 *       "educacao": 4.50,
 *       "administracao": 3.75
 *     },
 *     "alerts": []
 *   }
 * }
 */
router.get('/cost-report', ChatController.getCostReport);

/**
 * @route GET /api/chat/health
 * @desc Health check do sistema de cache
 * @access Public
 */
router.get('/health', async (req, res) => {
  try {
    const { cacheService } = await import('../services/cacheService');
    const { queueService } = await import('../services/queueService');
    const { isDiscountTime, getNextDiscountTime } = await import('../config/redis');
    
    const [cacheHealth, queueStatus] = await Promise.all([
      cacheService.healthCheck(),
      queueService.getQueueStatus(),
    ]);

    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        cache: cacheHealth ? 'online' : 'offline',
        queue: queueStatus ? 'online' : 'offline',
        discount: {
          active: isDiscountTime(),
          nextTime: getNextDiscountTime(),
        }
      },
      performance: {
        totalQueued: queueStatus?.totalQueued || 0,
        processing: queueStatus?.processingNow || 0,
      }
    };

    const statusCode = cacheHealth ? 200 : 503;
    res.status(statusCode).json(health);

  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Sistema de cache indisponível',
    });
  }
});

/**
 * @route GET /api/chat/discount-info
 * @desc Informações sobre horário de desconto
 * @access Public
 */
router.get('/discount-info', async (req, res) => {
  try {
    const { isDiscountTime, getNextDiscountTime } = await import('../config/redis');
    
    const now = new Date();
    const nextDiscount = getNextDiscountTime();
    const timeToDiscount = nextDiscount.getTime() - now.getTime();
    
    res.json({
      success: true,
      data: {
        discountActive: isDiscountTime(),
        currentTime: now.toISOString(),
        nextDiscountTime: nextDiscount.toISOString(),
        timeToDiscountMinutes: Math.max(Math.floor(timeToDiscount / 60000), 0),
        discountHours: 'UTC 16:30-00:30 (8 horas diárias)',
        savings: {
          percentage: '50%',
          costReduction: 'De $0.00219 para $0.001095 por mensagem',
          monthlyPotential: 'Até R$ 540 de economia mensal',
        },
        recommendation: isDiscountTime() ? 
          '🟢 Melhor momento para usar - 50% de desconto ativo!' :
          `🟡 Aguarde ${Math.floor(timeToDiscount / 60000)} minutos para 50% de desconto`
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Erro ao consultar informações de desconto'
    });
  }
});

/**
 * @route POST /api/chat/test-direct
 * @desc Teste direto sem middleware de cache ou auth
 * @access Public (apenas para debug)
 */
router.post('/test-direct', async (req, res) => {
  try {
    const { message = 'teste direto', secretaria = 'administracao' } = req.body;
    
    console.log(`🔧 Teste direto chat: ${message}`);
    
    const { processMessage } = await import('../services/deepSeekService');
    
    const context = {
      userId: 'test-user',
      secretaria: secretaria,
      history: [],
      metadata: {
        userName: 'Teste Direto',
        userRole: 'test'
      }
    };
    
    const startTime = Date.now();
    const response = await processMessage(message, context);
    const endTime = Date.now();
    
    res.json({
      success: true,
      response: response.content, // Compatibilidade com teste
      cacheHit: false, // Compatibilidade com teste
      data: {
        response: response.content,
        source: 'api',
        cost: response.cost.withDiscount,
        timestamp: response.timestamp
      },
      metadata: {
        urgency: 'test',
        discountActive: false,
        nextDiscountTime: new Date(),
        processingTime: endTime - startTime,
        tokens: response.tokens,
        model: response.model
      },
      debug: {
        message: message,
        context: context,
        bypassed: ['auth', 'cache', 'rateLimit']
      }
    });
    
  } catch (error: any) {
    console.error('❌ Erro no teste direto:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      debug: {
        fullError: error.toString(),
        stack: error.stack?.split('\n').slice(0, 5),
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * @route GET /api/chat/test-deepseek
 * @desc Teste direto da DeepSeek API sem autenticação
 * @access Public (apenas para debug)
 */
router.get('/test-deepseek', async (req, res) => {
  try {
    const { message = 'teste' } = req.query;
    
    console.log(`🔧 Teste direto DeepSeek API: ${message}`);
    
    const { processMessage } = await import('../services/deepSeekService');
    
    const context = {
      userId: 'test-user',
      secretaria: 'administracao',
      history: [],
      metadata: {
        userName: 'Teste',
        userRole: 'test'
      }
    };
    
    const startTime = Date.now();
    const response = await processMessage(message as string, context);
    const endTime = Date.now();
    
    res.json({
      success: true,
      data: {
        response: response.content,
        processingTime: endTime - startTime,
        tokens: response.tokens,
        cost: response.cost,
        model: response.model,
        timestamp: response.timestamp
      },
      debug: {
        message: message,
        context: context,
        apiStatus: 'conectado'
      }
    });
    
  } catch (error: any) {
    console.error('❌ Erro no teste DeepSeek:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      debug: {
        fullError: error.toString(),
        stack: error.stack?.split('\n').slice(0, 5),
        timestamp: new Date().toISOString()
      }
    });
  }
});

export default router;