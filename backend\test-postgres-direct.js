const { Client } = require('pg');

// Configuração de conexão direta
const client = new Client({
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
});

async function checkUsers() {
  console.log('=== VERIFICANDO USUÁRIOS NO POSTGRESQL (CONEXÃO DIRETA) ===\n');
  
  try {
    // Conectar ao banco
    await client.connect();
    console.log('✅ Conectado ao PostgreSQL!\n');
    
    // 1. Buscar usuários com "otto"
    console.log('1. Buscando usuários com "otto"...');
    const queryOtto = `
      SELECT id, nome, email, cpf, login, conta_ativa, servidor
      FROM usuarios 
      WHERE LOWER(nome) LIKE '%otto%' 
         OR LOWER(email) LIKE '%otto%'
         OR LOWER(cpf) LIKE '%otto%'
         OR LOWER(login) LIKE '%otto%'
      LIMIT 10
    `;
    
    const resultOtto = await client.query(queryOtto);
    
    if (resultOtto.rows.length > 0) {
      console.log(`\nEncontrados ${resultOtto.rows.length} usuários com "otto":`);
      resultOtto.rows.forEach(user => {
        console.log(`- ID: ${user.id}`);
        console.log(`  Nome: ${user.nome}`);
        console.log(`  Login: ${user.login || 'NULL'}`);
        console.log(`  Email: ${user.email || 'NULL'}`);
        console.log(`  CPF: ${user.cpf || 'NULL'}`);
        console.log(`  Ativo: ${user.conta_ativa}`);
        console.log(`  Servidor: ${user.servidor}\n`);
      });
    } else {
      console.log('❌ Nenhum usuário encontrado com "otto"');
    }
    
    // 2. Buscar usuários com "semantico"
    console.log('\n2. Buscando usuários com "semantico"...');
    const querySemantico = `
      SELECT id, nome, email, cpf, login, conta_ativa
      FROM usuarios 
      WHERE LOWER(nome) LIKE '%semantico%' 
         OR LOWER(email) LIKE '%semantico%'
         OR LOWER(login) LIKE '%semantico%'
      LIMIT 10
    `;
    
    const resultSemantico = await client.query(querySemantico);
    
    if (resultSemantico.rows.length > 0) {
      console.log(`\nEncontrados ${resultSemantico.rows.length} usuários com "semantico":`);
      resultSemantico.rows.forEach(user => {
        console.log(`- Nome: ${user.nome}, Login: ${user.login || 'NULL'}, Email: ${user.email || 'NULL'}`);
      });
    } else {
      console.log('❌ Nenhum usuário encontrado com "semantico"');
    }
    
    // 3. Verificar se a coluna "senha" existe e seu tipo
    console.log('\n3. Verificando estrutura da tabela usuarios...');
    const queryColumns = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'usuarios' 
      AND column_name IN ('senha', 'password', 'hash_senha', 'senha_hash')
    `;
    
    const resultColumns = await client.query(queryColumns);
    
    if (resultColumns.rows.length > 0) {
      console.log('\nColunas de senha encontradas:');
      resultColumns.rows.forEach(col => {
        console.log(`- ${col.column_name}: ${col.data_type}`);
      });
    } else {
      console.log('❌ Nenhuma coluna de senha encontrada');
    }
    
    // 4. Buscar alguns usuários ativos como exemplo
    console.log('\n4. Exemplos de usuários ativos (primeiros 5):');
    const queryExample = `
      SELECT id, nome, email, cpf, login
      FROM usuarios 
      WHERE conta_ativa = true
      LIMIT 5
    `;
    
    const resultExample = await client.query(queryExample);
    
    resultExample.rows.forEach((user, index) => {
      console.log(`\n${index + 1}. ${user.nome}`);
      console.log(`   Login: ${user.login || 'NULL'}`);
      console.log(`   Email: ${user.email || 'NULL'}`);
      console.log(`   CPF: ${user.cpf || 'NULL'}`);
    });
    
    // 5. Contar total de usuários
    console.log('\n5. Estatísticas gerais:');
    const totalResult = await client.query('SELECT COUNT(*) as total FROM usuarios');
    const activeResult = await client.query('SELECT COUNT(*) as total FROM usuarios WHERE conta_ativa = true');
    
    console.log(`- Total de usuários: ${totalResult.rows[0].total}`);
    console.log(`- Usuários ativos: ${activeResult.rows[0].total}`);
    
  } catch (error) {
    console.error('\n❌ Erro ao acessar PostgreSQL:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('Não foi possível conectar ao banco. Verifique se o servidor está acessível.');
    } else if (error.code === '42P01') {
      console.error('Tabela "usuarios" não encontrada.');
    }
  } finally {
    await client.end();
    console.log('\n✅ Conexão fechada.');
  }
}

// Executar verificação
checkUsers();