/**
 * Script de teste da integração completa de autenticação
 * Testa login real com dados do PostgreSQL
 */

import { authService } from '../services/authService';
import { postgresRepository } from '../repositories/PostgreSQLRepository';
import chalk from 'chalk';

async function testAuthIntegration() {
  console.log(chalk.blue('\n=== TESTE DE INTEGRAÇÃO - AUTENTICAÇÃO REAL ===\n'));

  try {
    // 1. Testar conexão com PostgreSQL
    console.log(chalk.yellow('1. Testando conexão PostgreSQL...'));
    const isConnected = await postgresRepository.testConnection();
    
    if (!isConnected) {
      console.log(chalk.red('❌ Falha na conexão PostgreSQL'));
      return;
    }
    console.log(chalk.green('✅ PostgreSQL conectado'));

    // 2. Buscar estatísticas
    console.log(chalk.yellow('\n2. Buscando estatísticas do banco...'));
    const stats = await postgresRepository.getStats();
    console.log(chalk.cyan(`   - Usuários ativos: ${stats.totalUsers}`));
    console.log(chalk.cyan(`   - Departamentos ativos: ${stats.totalDepartments}`));

    // 3. Listar alguns departamentos
    console.log(chalk.yellow('\n3. Listando departamentos disponíveis...'));
    const departments = await postgresRepository.getAllDepartments();
    console.log(chalk.cyan(`   Encontrados ${departments.length} departamentos:`));
    departments.slice(0, 5).forEach((dept, index) => {
      console.log(`   ${index + 1}. ID: ${dept.id} - ${dept.descricao}`);
    });

    // 4. Tentar login com diferentes credenciais
    console.log(chalk.yellow('\n4. Testando login com credenciais reais...'));

    // Teste 1: Buscar um usuário real do banco
    const testUser = await postgresRepository.findUserByEmailOrCPF('<EMAIL>');
    
    if (testUser) {
      console.log(chalk.cyan(`   Usuário encontrado: ${testUser.nome}`));
      console.log(chalk.cyan(`   Email: ${testUser.email}`));
      console.log(chalk.cyan(`   CPF: ${testUser.cpf}`));
      console.log(chalk.cyan(`   Conta ativa: ${testUser.conta_ativa}`));

      // Buscar departamentos do usuário
      const userDepts = await postgresRepository.getUserDepartments(testUser.id);
      console.log(chalk.cyan(`   Departamentos: ${userDepts.length}`));
      userDepts.slice(0, 3).forEach(dept => {
        console.log(`     - ${dept.descricao}`);
      });

      // Teste de login (sem senha real por segurança)
      console.log(chalk.yellow('\n   Teste de estrutura de login (sem senha real):'));
      const mockLoginTest = {
        login: testUser.email,
        password: 'senha-teste', // Não vai funcionar, mas testa estrutura
        secretaria: userDepts[0]?.descricao
      };

      const loginResult = await authService.login(mockLoginTest);
      
      if (loginResult.success) {
        console.log(chalk.green('   ✅ Estrutura de login funcionando'));
        console.log(chalk.cyan(`   Token gerado: ${loginResult.token?.substring(0, 50)}...`));
        console.log(chalk.cyan(`   Usuário: ${loginResult.user?.name}`));
      } else {
        console.log(chalk.orange('   ⚠️ Falha esperada (senha incorreta):'), loginResult.error);
      }
    }

    // 5. Teste de validação de token
    console.log(chalk.yellow('\n5. Testando geração e validação de JWT...'));
    
    // Criar um login fictício para testar JWT
    const mockValidUser = {
      id: 999,
      nome: 'Usuário Teste',
      email: '<EMAIL>',
      cpf: '000.000.000-00',
      conta_ativa: true,
      servidor: true
    };

    // Simular login bem-sucedido (sem validar senha)
    const mockDept = departments[0];
    const testLogin = await authService.login({
      login: mockValidUser.email,
      password: 'qualquer-senha' // Não vai funcionar mas testa estrutura
    });

    console.log(chalk.cyan('   Teste de estrutura JWT:'));
    console.log(chalk.cyan(`   - Login attempt result: ${testLogin.success ? 'Success' : 'Failed as expected'}`));

    // 6. Teste de mapeamento de secretarias
    console.log(chalk.yellow('\n6. Testando mapeamento de secretarias...'));
    const secretariasExemplo = [
      'Secretaria de Administração',
      'Secretaria de Saúde',
      'Secretaria de Educação',
      'Secretaria de Finanças'
    ];

    console.log(chalk.cyan('   Mapeamentos de secretaria:'));
    // Esta função é privada, então só mostramos o conceito
    secretariasExemplo.forEach(nome => {
      console.log(`   - "${nome}" → código gerado automaticamente`);
    });

    console.log(chalk.green('\n✅ TESTE DE INTEGRAÇÃO CONCLUÍDO COM SUCESSO'));
    console.log(chalk.yellow('\n📝 Próximos passos:'));
    console.log('   1. Testar login real com credenciais válidas');
    console.log('   2. Integrar middleware de autenticação nas rotas');  
    console.log('   3. Atualizar sistema de chat para usar usuários reais');
    console.log('   4. Configurar MongoDB para histórico de conversas');

  } catch (error) {
    console.log(chalk.red('\n❌ ERRO NO TESTE DE INTEGRAÇÃO:'));
    console.error(error);
  } finally {
    // Fechar conexões
    await postgresRepository.close();
  }
}

// Executar teste
testAuthIntegration().catch(console.error);