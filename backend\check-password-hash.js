const { Client } = require('pg');

const client = new Client({
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
});

async function checkPasswordHash() {
  console.log('=== VERIFICANDO SENHA DO USUÁRIO OTTO ===\n');
  
  try {
    await client.connect();
    console.log('✅ Conectado ao PostgreSQL!\n');
    
    // Buscar a senha do usuário Otto
    const query = `
      SELECT id, nome, email, cpf, senha, nova_senha, segunda_senha
      FROM usuarios 
      WHERE email = '<EMAIL>'
    `;
    
    const result = await client.query(query);
    
    if (result.rows.length > 0) {
      const user = result.rows[0];
      console.log('Usuário encontrado:');
      console.log(`- ID: ${user.id}`);
      console.log(`- Nome: ${user.nome}`);
      console.log(`- Email: ${user.email}`);
      console.log(`- CPF: ${user.cpf}`);
      console.log(`- Senha: ${user.senha ? user.senha.substring(0, 50) + '...' : 'NULL'}`);
      console.log(`- Nova Senha: ${user.nova_senha ? user.nova_senha.substring(0, 50) + '...' : 'NULL'}`);
      console.log(`- Segunda Senha: ${user.segunda_senha ? user.segunda_senha.substring(0, 20) + '...' : 'NULL'}`);
      
      // Verificar se a senha parece ser um hash
      if (user.senha) {
        console.log(`\nAnálise da senha:`);
        console.log(`- Comprimento: ${user.senha.length} caracteres`);
        console.log(`- Começa com $: ${user.senha.startsWith('$') ? 'SIM (provavelmente bcrypt)' : 'NÃO'}`);
        console.log(`- Tipo de hash: ${user.senha.startsWith('$2b$') ? 'bcrypt' : user.senha.startsWith('$2a$') ? 'bcrypt antigo' : 'desconhecido'}`);
        
        // Mostrar início da senha para análise
        console.log(`- Início: ${user.senha.substring(0, 20)}...`);
      }
      
    } else {
      console.log('❌ Usuário não encontrado');
    }
    
  } catch (error) {
    console.error('\n❌ Erro:', error.message);
  } finally {
    await client.end();
    console.log('\n✅ Conexão fechada.');
  }
}

checkPasswordHash();