/**
 * Teste direto do PostgreSQL sem dependências problemáticas
 */

import { Client } from 'pg';
import chalk from 'chalk';

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

async function testPostgresDirect() {
  console.log(chalk.blue('\n=== TESTE DIRETO POSTGRESQL ===\n'));

  const client = new Client(postgresConfig);
  
  try {
    // Conectar
    await client.connect();
    console.log(chalk.green('✅ Conexão estabelecida com sucesso'));

    // 1. Buscar estatísticas
    console.log(chalk.yellow('\n1. Estatísticas do banco:'));
    const userCount = await client.query('SELECT COUNT(*) as total FROM usuarios WHERE conta_ativa = true');
    const deptCount = await client.query('SELECT COUNT(*) as total FROM departamentos WHERE ativo = true');
    
    console.log(chalk.cyan(`   - Usuários ativos: ${userCount.rows[0].total}`));
    console.log(chalk.cyan(`   - Departamentos ativos: ${deptCount.rows[0].total}`));

    // 2. Buscar usuários de exemplo
    console.log(chalk.yellow('\n2. Usuários de exemplo (primeiros 5):'));
    const users = await client.query(`
      SELECT id, nome, email, cpf, conta_ativa, servidor 
      FROM usuarios 
      WHERE conta_ativa = true 
      LIMIT 5
    `);
    
    users.rows.forEach((user, index) => {
      console.log(chalk.gray(`   ${index + 1}. [${user.id}] ${user.nome}`));
      console.log(chalk.gray(`      Email: ${user.email || 'N/A'}`));
      console.log(chalk.gray(`      CPF: ${user.cpf}`));
      console.log(chalk.gray(`      Servidor: ${user.servidor ? 'Sim' : 'Não'}`));
    });

    // 3. Buscar departamentos de exemplo
    console.log(chalk.yellow('\n3. Departamentos de exemplo (primeiros 5):'));
    const departments = await client.query(`
      SELECT id, descricao, email, ativo 
      FROM departamentos 
      WHERE ativo = true 
      ORDER BY descricao
      LIMIT 5
    `);
    
    departments.rows.forEach((dept, index) => {
      console.log(chalk.gray(`   ${index + 1}. [${dept.id}] ${dept.descricao}`));
      console.log(chalk.gray(`      Email: ${dept.email || 'N/A'}`));
    });

    // 4. Testar busca de usuário específico
    console.log(chalk.yellow('\n4. Testando busca de usuário por email:'));
    const specificUser = await client.query(`
      SELECT * FROM usuarios 
      WHERE email = $1 AND conta_ativa = true
    `, ['<EMAIL>']);
    
    if (specificUser.rows.length > 0) {
      const user = specificUser.rows[0];
      console.log(chalk.green('✅ Usuário encontrado:'));
      console.log(chalk.cyan(`   - ID: ${user.id}`));
      console.log(chalk.cyan(`   - Nome: ${user.nome}`));
      console.log(chalk.cyan(`   - Email: ${user.email}`));
      console.log(chalk.cyan(`   - CPF: ${user.cpf}`));

      // Buscar departamentos do usuário
      console.log(chalk.yellow('\n5. Departamentos do usuário:'));
      const userDepts = await client.query(`
        SELECT d.id, d.descricao, d.email
        FROM departamentos d
        INNER JOIN user_departamentos ud ON d.id = ud.id_departamento
        WHERE ud.id_user = $1 AND d.ativo = true
      `, [user.id]);

      if (userDepts.rows.length > 0) {
        console.log(chalk.green(`✅ Encontrados ${userDepts.rows.length} departamentos:`));
        userDepts.rows.forEach((dept, index) => {
          console.log(chalk.cyan(`   ${index + 1}. [${dept.id}] ${dept.descricao}`));
        });
      } else {
        console.log(chalk.yellow('⚠️ Usuário não possui departamentos associados'));
      }
    } else {
      console.log(chalk.yellow('⚠️ Usuário não encontrado'));
    }

    // 6. Testar busca por CPF
    console.log(chalk.yellow('\n6. Testando busca por CPF:'));
    const userByCPF = await client.query(`
      SELECT nome, email, cpf FROM usuarios 
      WHERE cpf = $1 AND conta_ativa = true
    `, ['737.118.101-87']);

    if (userByCPF.rows.length > 0) {
      const user = userByCPF.rows[0];
      console.log(chalk.green('✅ Usuário encontrado por CPF:'));
      console.log(chalk.cyan(`   - Nome: ${user.nome}`));
      console.log(chalk.cyan(`   - Email: ${user.email}`));
    } else {
      console.log(chalk.yellow('⚠️ Usuário com este CPF não encontrado'));
    }

    // 7. Análise de relacionamentos
    console.log(chalk.yellow('\n7. Análise de relacionamentos user_departamentos:'));
    const relationships = await client.query(`
      SELECT COUNT(*) as total FROM user_departamentos
    `);
    console.log(chalk.cyan(`   - Total de relacionamentos: ${relationships.rows[0].total}`));

    console.log(chalk.green('\n✅ TESTE DIRETO CONCLUÍDO COM SUCESSO'));
    
    console.log(chalk.magenta('\n🎯 ESTRUTURA MAPEADA E PRONTA:'));
    console.log(chalk.yellow('   ✅ Tabela usuarios: campos mapeados'));
    console.log(chalk.yellow('   ✅ Tabela departamentos: campos mapeados'));
    console.log(chalk.yellow('   ✅ Tabela user_departamentos: relacionamento funcional'));
    console.log(chalk.yellow('   ✅ Queries SQL: testadas e funcionando'));

    console.log(chalk.cyan('\n📋 Integração PostgreSQL: PRONTA PARA USO'));

  } catch (error) {
    console.log(chalk.red('❌ Erro no teste direto:'));
    console.error(error);
  } finally {
    await client.end();
  }
}

// Executar teste
testPostgresDirect().catch(console.error);