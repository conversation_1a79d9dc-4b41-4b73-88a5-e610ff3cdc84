# Sistema RAG - Prefeitura de Valparaíso

## 🎯 Visão Geral

Sistema de RAG (Retrieval-Augmented Generation) implementado para melhorar a precisão das respostas do chatbot municipal usando busca semântica em dados específicos da Prefeitura de Valparaíso de Goiás.

## 🚀 Início Rápido

### Windows
```bash
# Executar script automático
./start-rag-system.bat
```

### Linux/Mac
```bash
# Executar script automático
./start-rag-system.sh
```

## 📋 Requisitos

### Opção 1: Docker (Recomendado)
- Docker Desktop instalado
- Docker Compose disponível

### Opção 2: Python
- Python 3.8+ instalado
- pip funcionando

## 🔧 Configuração Manual

### 1. Iniciar ChromaDB Server

**Com Docker:**
```bash
docker-compose up -d chromadb
```

**Com Python:**
```bash
pip install chromadb uvicorn
python start-chroma-server.py
```

### 2. Verificar ChromaDB
```bash
curl http://localhost:8000/api/v1/heartbeat
# Deve retornar: {"nanosecond heartbeat": ...}
```

### 3. Inicializar Base Vetorial
```bash
cd backend
npx tsx src/scripts/initialize-rag.ts
```

### 4. Iniciar Backend
```bash
cd backend
npm run dev
```

## 🧪 Testes

### Teste de Connectividade
```bash
cd backend
node -e "
const { getRagService } = require('./dist/services/ragService.js');
getRagService().testConnection().then(console.log);
"
```

### Teste Comparativo RAG vs Não-RAG
```bash
cd backend
npx tsx src/scripts/test-rag-comparison.ts
```

### Teste de Busca Manual
```bash
cd backend
node -e "
const { getRagService } = require('./dist/services/ragService.js');
const rag = getRagService();
rag.searchSimilar('Como solicitar alvará de construção?', {secretaria: 'obras'})
  .then(results => console.log(JSON.stringify(results, null, 2)));
"
```

## 📊 Arquitetura

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │     Backend      │    │   ChromaDB      │
│   (Next.js)     │◄──►│   (Express)      │◄──►│   (Vector DB)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   OpenAI API     │
                       │   (Embeddings)   │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   DeepSeek V3    │
                       │   (Chat)         │
                       └──────────────────┘
```

## 🗄️ Estrutura de Dados

- **92 chunks** de conhecimento municipal
- **7 secretarias** cobertas
- **50 departamentos** mapeados
- **20 serviços** específicos
- **20 formulários** catalogados

## ⚙️ Configurações (.env)

```bash
# RAG Configuration
RAG_ENABLED=true
RAG_TOP_K=3
RAG_SIMILARITY_THRESHOLD=0.7
RAG_CHUNK_SIZE=500
RAG_CHUNK_OVERLAP=50

# ChromaDB
CHROMA_SERVER_URL=http://localhost:8000

# OpenAI (para embeddings)
OPENAI_API_KEY=sk-proj-wi7zpkie5...
OPENAI_EMBEDDING_MODEL=text-embedding-3-small
```

## 📈 Métricas Esperadas

- **Precisão**: 68.7% → 85%+ (+25% melhoria)
- **Relevância contextual**: 30% → 80%+
- **Tempo de resposta**: +3-5s (aceitável)
- **Custo adicional**: ~5% (embeddings)

## 🔍 Como Funciona

1. **Preparação**: Dados municipais são processados em chunks semânticos
2. **Vetorização**: OpenAI cria embeddings para cada chunk
3. **Armazenamento**: Vetores são salvos no ChromaDB
4. **Busca**: Query do usuário é vetorizada e comparada
5. **Recuperação**: Top-K documentos mais similares são recuperados
6. **Geração**: DeepSeek usa documentos + prompt para resposta

## 🛠️ Troubleshooting

### ChromaDB não inicia
```bash
# Verificar se porta 8000 está livre
netstat -tulpn | grep :8000

# Parar processo na porta
kill -9 $(lsof -ti:8000)

# Reiniciar ChromaDB
docker-compose restart chromadb
```

### Erro de embeddings
```bash
# Verificar chave OpenAI
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     https://api.openai.com/v1/models
```

### Vector store vazio
```bash
# Verificar se dados existem
ls -la backend/knowledge/
ls -la backend/rag-data/

# Recriar base vetorial
cd backend
npx tsx src/scripts/initialize-rag.ts
```

## 📝 Comandos Úteis

```bash
# Status dos serviços
docker-compose ps

# Logs do ChromaDB
docker-compose logs chromadb

# Resetar ChromaDB
docker-compose down -v
docker-compose up -d chromadb

# Rebuild da base vetorial
rm -rf backend/rag-data/chroma-db/*
cd backend && npx tsx src/scripts/initialize-rag.ts

# Monitor de custos OpenAI
cd backend && node -e "console.log('Tokens estimados hoje:', process.env.OPENAI_TOKENS_TODAY || 0)"
```

## 🎯 Próximos Passos

1. ✅ Sistema RAG funcionando
2. 🔄 Integração com frontend
3. 📊 Dashboard de métricas
4. 🚀 Deploy em produção
5. 📈 Otimização contínua

---

## 🔄 Atualizações de Versão (23/01/2025)

### ✅ Versões Confirmadas e Funcionando:
- **DeepSeek V3**: Confirmado via teste direto (128K context, knowledge julho 2024)
- **ChromaDB API v2**: Servidor respondendo corretamente (/api/v2/heartbeat)
- **Vector Store**: 276 documentos (92 chunks originais + testes)
- **Performance**: RAG retrieval em 750-1400ms, respostas completas 25-30s

### 🧪 Testes de Validação Realizados:
```bash
# Teste DeepSeek V3
✅ Modelo confirmado: "Yes! I am DeepSeek-V3"
✅ Context length: 128K tokens
✅ Performance: 25-30s por resposta

# Teste ChromaDB API v2  
✅ curl http://localhost:8000/api/v2/version → "1.0.0"
✅ curl http://localhost:8000/api/v2/heartbeat → nanosecond heartbeat

# Teste RAG Integração
✅ 3/3 consultas com documentos encontrados via RAG
✅ Custos: $0.0003-$0.0005 por consulta
✅ Fallback automático funcionando
```

---

**Status**: ✅ Implementado, testado e versões validadas  
**Versão**: 1.1 (versões atualizadas)  
**Data**: 23 de Janeiro 2025