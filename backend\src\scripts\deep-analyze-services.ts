/**
 * Análise profunda de serviços, protocolos e solicitações
 * para extrair conhecimento específico para os prompts
 */

import { Client } from 'pg';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';

const postgresConfig = {
  host: '*************',
  port: 5411,
  user: 'otto',
  password: 'otto',
  database: 'pv_valparaiso'
};

interface ServiceKnowledge {
  servicos: any[];
  solicitacoes: any[];
  protocolos: any[];
  formularios: any[];
  procedimentos: any[];
  estatisticas: any;
}

async function analyzeServicesInDepth(): Promise<ServiceKnowledge> {
  console.log(chalk.blue('\n=== ANÁLISE PROFUNDA DE SERVIÇOS E PROCEDIMENTOS ===\n'));
  
  const client = new Client(postgresConfig);
  const knowledge: ServiceKnowledge = {
    servicos: [],
    solicitacoes: [],
    protocolos: [],
    formularios: [],
    procedimentos: [],
    estatisticas: {}
  };

  try {
    await client.connect();
    console.log(chalk.green('✅ Conectado ao PostgreSQL'));

    // 1. ANALISAR TABELA DE SERVIÇOS
    console.log(chalk.yellow('\n🎯 Analisando serviços disponíveis:'));
    
    try {
      const servicosResult = await client.query(`
        SELECT * FROM servicos 
        ORDER BY id 
        LIMIT 20
      `);
      
      knowledge.servicos = servicosResult.rows;
      console.log(chalk.cyan(`✅ Encontrados ${servicosResult.rows.length} serviços`));
      
      servicosResult.rows.forEach((servico, index) => {
        console.log(`  ${index + 1}. ${servico.nome || servico.descricao || servico.titulo || 'N/A'}`);
      });
    } catch (error) {
      console.log(chalk.red('❌ Tabela servicos não acessível'));
    }

    // 2. ANALISAR SERVIÇOS ONLINE
    console.log(chalk.yellow('\n🌐 Analisando serviços online:'));
    
    try {
      const servicosOnlineResult = await client.query(`
        SELECT * FROM servicos_onlines 
        ORDER BY id 
        LIMIT 20
      `);
      
      console.log(chalk.cyan(`✅ Encontrados ${servicosOnlineResult.rows.length} serviços online`));
      
      servicosOnlineResult.rows.forEach((servico, index) => {
        console.log(`  ${index + 1}. ${servico.nome || servico.descricao || servico.titulo || 'N/A'}`);
      });
    } catch (error) {
      console.log(chalk.red('❌ Tabela servicos_onlines não acessível'));
    }

    // 3. ANALISAR SOLICITAÇÕES
    console.log(chalk.yellow('\n📋 Analisando solicitações dos cidadãos:'));
    
    try {
      // Verificar estrutura da tabela solicitacoes
      const solicitacoesStructure = await client.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'solicitacoes' 
        ORDER BY ordinal_position
      `);
      
      console.log(chalk.cyan('Estrutura da tabela solicitacoes:'));
      solicitacoesStructure.rows.forEach(col => {
        console.log(`  - ${col.column_name} (${col.data_type})`);
      });

      // Buscar amostras de solicitações
      const solicitacoesResult = await client.query(`
        SELECT * FROM solicitacoes 
        ORDER BY id DESC 
        LIMIT 10
      `);
      
      knowledge.solicitacoes = solicitacoesResult.rows;
      console.log(chalk.cyan(`✅ Encontradas ${solicitacoesResult.rows.length} solicitações recentes`));
      
    } catch (error) {
      console.log(chalk.red('❌ Tabela solicitacoes não acessível'));
    }

    // 4. ANALISAR PROTOCOLOS VIRTUAIS
    console.log(chalk.yellow('\n📄 Analisando protocolos virtuais:'));
    
    try {
      const protocolosResult = await client.query(`
        SELECT * FROM protocolo_virtual_processos 
        ORDER BY id DESC 
        LIMIT 10
      `);
      
      knowledge.protocolos = protocolosResult.rows;
      console.log(chalk.cyan(`✅ Encontrados ${protocolosResult.rows.length} protocolos virtuais`));
      
    } catch (error) {
      console.log(chalk.red('❌ Tabela protocolo_virtual_processos não acessível'));
    }

    // 5. ANALISAR ASSUNTOS DE PROTOCOLOS
    console.log(chalk.yellow('\n📝 Analisando assuntos de protocolos:'));
    
    try {
      const assuntosResult = await client.query(`
        SELECT id, nome, descricao FROM protocolo_virtual_assuntos 
        ORDER BY nome 
        LIMIT 50
      `);
      
      console.log(chalk.cyan(`✅ Encontrados ${assuntosResult.rows.length} tipos de assuntos`));
      
      assuntosResult.rows.forEach((assunto, index) => {
        console.log(`  ${index + 1}. ${assunto.nome} - ${assunto.descricao || 'N/A'}`);
      });
      
    } catch (error) {
      console.log(chalk.red('❌ Tabela protocolo_virtual_assuntos não acessível'));
    }

    // 6. ANALISAR FORMULÁRIOS DINÂMICOS
    console.log(chalk.yellow('\n📋 Analisando formulários disponíveis:'));
    
    try {
      const formulariosResult = await client.query(`
        SELECT * FROM formulario 
        ORDER BY nome 
        LIMIT 20
      `);
      
      knowledge.formularios = formulariosResult.rows;
      console.log(chalk.cyan(`✅ Encontrados ${formulariosResult.rows.length} formulários`));
      
      formulariosResult.rows.forEach((form, index) => {
        console.log(`  ${index + 1}. ${form.nome} - ${form.descricao || 'N/A'}`);
      });
      
    } catch (error) {
      console.log(chalk.red('❌ Tabela formulario não acessível'));
    }

    // 7. ANALISAR REQUISIÇÕES E ATIVIDADES
    console.log(chalk.yellow('\n⚙️ Analisando requisições e atividades:'));
    
    try {
      const requisicoesResult = await client.query(`
        SELECT COUNT(*) as total FROM requisicao
      `);
      
      const atividadesResult = await client.query(`
        SELECT nome FROM requisicao_atividade 
        ORDER BY nome 
        LIMIT 30
      `);
      
      console.log(chalk.cyan(`✅ Total de requisições: ${requisicoesResult.rows[0].total}`));
      console.log(chalk.cyan(`✅ Atividades disponíveis: ${atividadesResult.rows.length}`));
      
      atividadesResult.rows.forEach((ativ, index) => {
        console.log(`  ${index + 1}. ${ativ.nome}`);
      });
      
    } catch (error) {
      console.log(chalk.red('❌ Tabelas de requisição não acessíveis'));
    }

    // 8. ANALISAR ÁREA DA SAÚDE (se disponível)
    console.log(chalk.yellow('\n🏥 Analisando sistema de saúde:'));
    
    try {
      const especialidadesResult = await client.query(`
        SELECT nome FROM saude_especialidades 
        ORDER BY nome 
        LIMIT 20
      `);
      
      console.log(chalk.cyan(`✅ Especialidades médicas: ${especialidadesResult.rows.length}`));
      
      especialidadesResult.rows.forEach((esp, index) => {
        console.log(`  ${index + 1}. ${esp.nome}`);
      });
      
    } catch (error) {
      console.log(chalk.red('❌ Sistema de saúde não acessível'));
    }

    // 9. ANALISAR ÁREA DA EDUCAÇÃO (PNAE)
    console.log(chalk.yellow('\n🎓 Analisando sistema de educação (PNAE):'));
    
    try {
      const pnaeResult = await client.query(`
        SELECT COUNT(*) as total_cardapios FROM pnae_cardapios
      `);
      
      const produtosResult = await client.query(`
        SELECT nome FROM pnae_produtos 
        ORDER BY nome 
        LIMIT 10
      `);
      
      console.log(chalk.cyan(`✅ Total de cardápios PNAE: ${pnaeResult.rows[0].total_cardapios}`));
      console.log(chalk.cyan(`✅ Produtos disponíveis: ${produtosResult.rows.length}`));
      
    } catch (error) {
      console.log(chalk.red('❌ Sistema PNAE não acessível'));
    }

    // 10. ANALISAR LEGISLATIVO
    console.log(chalk.yellow('\n🏛️ Analisando sistema legislativo:'));
    
    try {
      const legisladoresResult = await client.query(`
        SELECT nome FROM plenario_legisladores 
        ORDER BY nome 
        LIMIT 20
      `);
      
      const materiasResult = await client.query(`
        SELECT COUNT(*) as total FROM plenario_materia
      `);
      
      console.log(chalk.cyan(`✅ Legisladores: ${legisladoresResult.rows.length}`));
      console.log(chalk.cyan(`✅ Total de matérias: ${materiasResult.rows[0].total}`));
      
    } catch (error) {
      console.log(chalk.red('❌ Sistema legislativo não acessível'));
    }

    return knowledge;

  } catch (error) {
    console.error(chalk.red('❌ Erro na análise profunda:'), error);
    throw error;
  } finally {
    await client.end();
  }
}

// Executar análise
async function main() {
  try {
    const knowledge = await analyzeServicesInDepth();
    
    // Salvar conhecimento
    const outputDir = path.join(__dirname, '..', '..', 'knowledge');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const outputFile = path.join(outputDir, 'services-deep-analysis.json');
    fs.writeFileSync(outputFile, JSON.stringify(knowledge, null, 2), 'utf8');
    
    console.log(chalk.green(`\n💾 Análise profunda salva em: ${outputFile}`));
    console.log(chalk.green('\n🎉 Análise profunda concluída com sucesso!'));
    
  } catch (error) {
    console.error(chalk.red('\n❌ Erro na análise:'), error);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

export { analyzeServicesInDepth };