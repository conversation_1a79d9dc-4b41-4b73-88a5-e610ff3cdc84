// Classificador de urgência para mensagens do chatbot
export type UrgencyLevel = 'immediate' | 'normal' | 'batch';

export interface UrgencyResult {
  level: UrgencyLevel;
  confidence: number;
  keywords: string[];
  reason: string;
}

export interface ClassificationRules {
  immediate: {
    keywords: string[];
    patterns: RegExp[];
    weight: number;
  };
  normal: {
    keywords: string[];
    patterns: RegExp[];
    weight: number;
  };
  batch: {
    keywords: string[];
    patterns: RegExp[];
    weight: number;
  };
}

class UrgencyClassifier {
  private rules: ClassificationRules = {
    immediate: {
      keywords: [
        // Palavras de emergência
        'emergência', 'emergencia', 'urgente', 'imediato', 'agora',
        'crítico', 'critico', 'problema', 'erro', 'falha',
        
        // Contexto temporal urgente
        'hoje', 'amanhã', 'manhã', 'tarde', 'reunião',
        'prazo', 'vencimento', 'deadline',
        
        // Situações críticas
        'parado', 'não funciona', 'nao funciona', 'quebrado',
        'bloqueado', 'travado', 'fora do ar',
        
        // Contexto de atendimento
        'cidadão esperando', 'cidadao esperando', 'fila',
        'atendimento parado', 'sistema fora',
      ],
      patterns: [
        /preciso\s+(agora|hoje|imediatamente)/i,
        /reunião\s+(em|dentro de)\s+\d+\s+(minutos?|horas?)/i,
        /prazo\s+(hoje|amanhã|manhã)/i,
        /não\s+(funciona|está funcionando)/i,
        /sistema\s+(parado|fora|com problema)/i,
      ],
      weight: 3
    },
    
    normal: {
      keywords: [
        // Consultas gerais
        'consulta', 'informação', 'informacao', 'dúvida', 'duvida',
        'status', 'situação', 'situacao', 'andamento',
        
        // Dados e relatórios simples
        'quantos', 'qual', 'como', 'onde', 'quando',
        'lista', 'dados', 'informações', 'informacoes',
        
        // Processos administrativos
        'processo', 'protocolo', 'documento', 'arquivo',
        'tramitação', 'tramitacao', 'procedimento',
      ],
      patterns: [
        /como\s+(posso|faço|fazer)/i,
        /qual\s+(é|o|a)\s+/i,
        /quantos?\s+/i,
        /onde\s+(fica|está|encontro)/i,
        /status\s+(do|da|de)/i,
      ],
      weight: 2
    },
    
    batch: {
      keywords: [
        // Relatórios complexos
        'relatório', 'relatorio', 'relatórios', 'relatorios',
        'estatística', 'estatistica', 'estatísticas', 'estatisticas',
        'análise', 'analise', 'dashboard', 'indicadores',
        
        // Dados históricos
        'histórico', 'historico', 'mensal', 'anual',
        'comparativo', 'evolução', 'evolucao', 'tendência', 'tendencia',
        
        // Processamento pesado
        'todos os', 'todas as', 'completo', 'detalhado',
        'consolidado', 'geral', 'resumo executivo',
      ],
      patterns: [
        /relatório\s+(completo|detalhado|mensal|anual)/i,
        /dados\s+(históricos|de todos|completos)/i,
        /análise\s+(completa|detalhada|geral)/i,
        /estatísticas?\s+(gerais|completas|de)/i,
        /(todos os|todas as)\s+/i,
      ],
      weight: 1
    }
  };

  // Classificar urgência da mensagem
  classify(message: string, context?: {
    timeOfDay?: 'morning' | 'afternoon' | 'evening';
    userRole?: 'admin' | 'gestor' | 'operador' | 'consulta';
    secretaria?: string;
  }): UrgencyResult {
    const messageLower = message.toLowerCase();
    const scores = {
      immediate: 0,
      normal: 0,
      batch: 0
    };
    
    const matchedKeywords: string[] = [];
    
    // Verificar palavras-chave
    for (const [level, config] of Object.entries(this.rules)) {
      for (const keyword of config.keywords) {
        if (messageLower.includes(keyword)) {
          scores[level as UrgencyLevel] += config.weight;
          matchedKeywords.push(keyword);
        }
      }
      
      // Verificar padrões regex
      for (const pattern of config.patterns) {
        if (pattern.test(message)) {
          scores[level as UrgencyLevel] += config.weight * 1.5; // Padrões têm peso maior
          matchedKeywords.push(`pattern:${pattern.source.substring(0, 20)}...`);
        }
      }
    }
    
    // Aplicar modificadores de contexto
    if (context) {
      this.applyContextModifiers(scores, context, messageLower);
    }
    
    // Determinar nível final
    const maxScore = Math.max(...Object.values(scores));
    const level = (Object.keys(scores) as UrgencyLevel[])
      .find(key => scores[key] === maxScore) || 'normal';
    
    // Calcular confiança
    const totalScore = Object.values(scores).reduce((a, b) => a + b, 0);
    const confidence = totalScore > 0 ? (maxScore / totalScore) * 100 : 50;
    
    return {
      level,
      confidence,
      keywords: matchedKeywords,
      reason: this.generateReason(level, matchedKeywords, confidence)
    };
  }

  // Aplicar modificadores baseados no contexto
  private applyContextModifiers(
    scores: Record<UrgencyLevel, number>,
    context: NonNullable<Parameters<typeof this.classify>[1]>,
    message: string
  ): void {
    // Modificador por horário do dia
    if (context.timeOfDay === 'evening') {
      scores.batch += 0.5; // Relatórios são mais comuns à noite
    }
    
    // Modificador por role do usuário
    if (context.userRole === 'admin') {
      scores.immediate += 0.5; // Admins têm prioridade
    } else if (context.userRole === 'consulta') {
      scores.batch += 0.5; // Consultas podem aguardar
    }
    
    // Modificador por secretaria
    if (context.secretaria === 'saude' && message.includes('emergência')) {
      scores.immediate += 2; // Emergências de saúde são críticas
    }
    
    // Modificador por tamanho da mensagem
    if (message.length > 200) {
      scores.batch += 1; // Mensagens longas geralmente são relatórios
    }
  }

  // Gerar explicação da classificação
  private generateReason(level: UrgencyLevel, keywords: string[], confidence: number): string {
    const reasons = {
      immediate: [
        'Detectadas palavras de urgência',
        'Contexto temporal crítico identificado',
        'Situação de emergência detectada',
        'Problema operacional identificado'
      ],
      normal: [
        'Consulta padrão identificada',
        'Pergunta informacional detectada',
        'Processo administrativo normal',
        'Solicitação de dados simples'
      ],
      batch: [
        'Solicitação de relatório complexo',
        'Análise de dados históricos',
        'Processamento de grandes volumes',
        'Relatório executivo detectado'
      ]
    };
    
    const baseReason = reasons[level][Math.floor(Math.random() * reasons[level].length)];
    
    if (keywords.length > 0) {
      const keywordList = keywords.slice(0, 3).join(', ');
      return `${baseReason}. Palavras-chave: ${keywordList}. Confiança: ${confidence.toFixed(1)}%`;
    }
    
    return `${baseReason}. Confiança: ${confidence.toFixed(1)}%`;
  }

  // Validar se uma mensagem pode aguardar o desconto
  canWaitForDiscount(message: string, maxWaitHours: number = 8): boolean {
    const classification = this.classify(message);
    
    // Nunca fazer urgências esperarem
    if (classification.level === 'immediate') {
      return false;
    }
    
    // Se a confiança na classificação é baixa, ser conservador
    if (classification.confidence < 70) {
      return false;
    }
    
    // Batch sempre pode esperar
    if (classification.level === 'batch') {
      return true;
    }
    
    // Normal pode esperar se não tiver indicadores de urgência
    return !classification.keywords.some(keyword => 
      ['hoje', 'agora', 'reunião', 'prazo'].includes(keyword)
    );
  }

  // Sugerir melhor horário para processar
  suggestOptimalTime(message: string): {
    immediate: boolean;
    waitForDiscount: boolean;
    estimatedSavings: number;
  } {
    const classification = this.classify(message);
    
    if (classification.level === 'immediate') {
      return {
        immediate: true,
        waitForDiscount: false,
        estimatedSavings: 0
      };
    }
    
    // Calcular economia estimada (50% de desconto)
    const estimatedCost = 0.00219; // Custo médio por mensagem
    const estimatedSavings = estimatedCost * 0.5;
    
    return {
      immediate: false,
      waitForDiscount: classification.level === 'batch' || 
                      (classification.level === 'normal' && classification.confidence > 80),
      estimatedSavings
    };
  }
}

export const urgencyClassifier = new UrgencyClassifier();
export default urgencyClassifier;