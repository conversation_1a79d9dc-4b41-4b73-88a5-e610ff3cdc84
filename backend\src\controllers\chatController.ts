import { Response } from 'express';
import { CachedRequest, CacheMiddlewareResponse } from '../middleware/cacheMiddleware';
import { cacheService } from '../services/cacheService';
import { queueService } from '../services/queueService';
import { isDiscountTime, getNextDiscountTime } from '../config/redis';
import { processMessage as processWithDeepSeek } from '../services/deepSeekService';
import { costMonitoringService } from '../services/costMonitoringService';

class ChatController {
  // Endpoint principal para processamento de mensagens
  static async processMessage(req: CachedRequest, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      const { message, secretaria } = req.body;
      const context = req.cacheContext;

      if (!context) {
        res.status(500).json({
          success: false,
          error: 'Contexto de cache não encontrado - middleware não executou corretamente'
        });
        return;
      }

      console.log(`🚀 Processamento IMEDIATO para ${secretaria}`);

      // Processar com DeepSeek API real
      console.log(`🤖 Processando mensagem para ${secretaria}: ${message.substring(0, 50)}...`);
      
      // Preparar contexto para DeepSeek
      const deepSeekContext = {
        userId: context.userId,
        secretaria: secretaria,
        history: req.conversationHistory || [],
        metadata: {
          userName: req.user?.name,
          userRole: req.user?.role,
        }
      };

      // Chamar DeepSeek API
      const deepSeekResponse = await processWithDeepSeek(message, deepSeekContext);

      // Extrair dados da resposta
      const response = deepSeekResponse.content;
      const cost = deepSeekResponse.cost.withDiscount;
      const tokens = deepSeekResponse.tokens;

      // Salvar no cache para futuras consultas
      await cacheService.setExactCache(
        message,
        context,
        response,
        cost,
        tokens
      );

      // Registrar custo para monitoramento
      await costMonitoringService.recordCost({
        timestamp: new Date(),
        userId: context.userId,
        secretaria: secretaria,
        cost: cost,
        tokens: tokens,
        cacheHit: false,
        discountApplied: isDiscountTime()
      });

      const result: CacheMiddlewareResponse = {
        success: true,
        response: response, // Compatibilidade com teste
        cacheHit: false,
        data: {
          response,
          source: 'api',
          cost,
          timestamp: new Date(),
        },
        metadata: {
          urgency: req.urgencyInfo?.level || 'normal',
          discountActive: isDiscountTime(),
          nextDiscountTime: getNextDiscountTime(),
          processingTime: Date.now() - startTime,
          tokens: tokens,
          model: 'deepseek-chat',
          rateLimit: req.rateLimitInfo ? {
            messagesRemaining: req.rateLimitInfo.daily.messagesRemaining - 1,
            tokensRemaining: req.rateLimitInfo.daily.tokensRemaining - tokens.total,
            resetTime: req.rateLimitInfo.resetTime
          } : undefined
        }
      };

      res.json(result);

    } catch (error) {
      console.error('Erro no processamento da mensagem:', error);
      
      const result: CacheMiddlewareResponse = {
        success: false,
        error: 'Erro interno no processamento da mensagem',
        metadata: {
          urgency: 'error',
          discountActive: isDiscountTime(),
          nextDiscountTime: getNextDiscountTime(),
          processingTime: Date.now() - startTime,
        }
      };

      res.status(500).json(result);
    }
  }

  // [DEPRECATED] Mantido para fallback - Gerar resposta específica por secretaria
  private static generateSecretariaResponse(message: string, secretaria: string): string {
    const responses = {
      'administracao': `Como assistente da Secretaria de Administração, posso ajudá-lo com informações sobre:
        
Baseado na sua pergunta "${message.substring(0, 100)}...", vou consultar nossos sistemas administrativos.

📊 **Dados Encontrados:**
- Estrutura organizacional atualizada
- Informações de recursos humanos
- Processos administrativos vigentes
- Contratos e licitações em andamento

💡 **Análise Inteligente:**
A consulta indica necessidade de informações sobre gestão administrativa. Nossos sistemas mostram que temos 156 processos ativos e 23 licitações em andamento.

🔍 **Recomendações:**
1. Verificar documentação específica no protocolo digital
2. Consultar manual de procedimentos atualizado
3. Considerar abertura de processo específico se necessário

*Resposta gerada com economia de 50% no horário de desconto ativo.*`,

      'saude': `Como assistente especializado da Secretaria de Saúde, analisei sua solicitação:

🏥 **Informações de Saúde Municipal:**
Sobre "${message.substring(0, 100)}...", consultei nossos sistemas de saúde.

📈 **Indicadores Atuais:**
- 12 Unidades Básicas de Saúde em funcionamento
- 45 profissionais de saúde ativos
- 234 atendimentos realizados esta semana
- 89% de cobertura vacinal atual

⚕️ **Análise Epidemiológica:**
Os dados mostram situação controlada com indicadores dentro da normalidade para o período.

📋 **Ações Recomendadas:**
1. Manter acompanhamento epidemiológico
2. Continuar campanhas preventivas
3. Avaliar necessidade de recursos adicionais

*Processado com IA especializada em gestão pública de saúde.*`,

      'educacao': `Como assistente da Secretaria de Educação, analisei sua consulta:

📚 **Dados Educacionais:**
Referente à sua pergunta "${message.substring(0, 100)}...", acessei nossos sistemas educacionais.

🎓 **Situação Atual:**
- 28 escolas municipais ativas
- 340 professores no quadro
- 5.240 alunos matriculados
- 94% de frequência escolar média

📊 **Indicadores de Qualidade:**
- IDEB municipal: 6.2 (meta: 6.0)
- Taxa de aprovação: 96%
- Evasão escolar: 2.1%

🎯 **Projetos em Andamento:**
1. Programa de Alfabetização Digital
2. Merenda Escolar Orgânica
3. Capacitação Continuada de Professores

*Dados atualizados em tempo real com nossos sistemas educacionais.*`,

      'financas': `Como assistente da Secretaria de Finanças, analisei sua solicitação financeira:

💰 **Informações Orçamentárias:**
Sobre "${message.substring(0, 100)}...", consultei nossos sistemas financeiros.

📊 **Situação Fiscal Atual:**
- Receita arrecadada no mês: R$ 2.340.567,89
- Execução orçamentária: 78%
- Disponibilidade de caixa: R$ 890.234,56
- Índice de transparência: 98%

📈 **Análise Financeira:**
Demonstrativo indica saúde fiscal positiva com cumprimento da LRF e metas fiscais.

📋 **Recomendações:**
1. Manter controle rigoroso de gastos
2. Otimizar arrecadação municipal
3. Avaliar investimentos prioritários

*Dados extraídos dos sistemas contábeis oficiais em tempo real.*`
    };

    return responses[secretaria as keyof typeof responses] || 
           `Consulta processada para a secretaria de ${secretaria}. 
            Baseado na sua pergunta "${message}", nossa IA municipal analisou 
            os dados disponíveis e gerou esta resposta personalizada.`;
  }

  // Obter status das filas do usuário
  static async getUserQueueStatus(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        res.status(400).json({
          success: false,
          error: 'UserId obrigatório'
        });
        return;
      }

      const [queuedMessages, queueStatus] = await Promise.all([
        queueService.getUserQueuedMessages(userId),
        queueService.getQueueStatus(),
      ]);

      res.json({
        success: true,
        data: {
          userMessages: queuedMessages,
          globalStatus: queueStatus,
          discountInfo: {
            isActive: isDiscountTime(),
            nextTime: getNextDiscountTime(),
            savings: '50% de economia no horário de desconto',
          }
        }
      });

    } catch (error) {
      console.error('Erro ao obter status da fila:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao consultar filas'
      });
    }
  }

  // Cancelar mensagem na fila
  static async cancelQueuedMessage(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { messageId, userId } = req.body;
      
      if (!messageId || !userId) {
        res.status(400).json({
          success: false,
          error: 'MessageId e userId obrigatórios'
        });
        return;
      }

      const cancelled = await queueService.cancelQueuedMessage(messageId, userId);
      
      if (cancelled) {
        res.json({
          success: true,
          message: 'Mensagem cancelada com sucesso'
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Mensagem não encontrada na fila ou já processada'
        });
      }

    } catch (error) {
      console.error('Erro ao cancelar mensagem:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao cancelar mensagem'
      });
    }
  }

  // Promover mensagem para processamento imediato
  static async promoteMessage(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { messageId, userId } = req.body;
      
      if (!messageId || !userId) {
        res.status(400).json({
          success: false,
          error: 'MessageId e userId obrigatórios'
        });
        return;
      }

      const promoted = await queueService.promoteToImmediate(messageId, userId);
      
      if (promoted) {
        res.json({
          success: true,
          message: 'Mensagem promovida para processamento imediato',
          note: 'Será processada sem desconto em alguns segundos'
        });
      } else {
        res.status(404).json({
          success: false,
          error: 'Mensagem não encontrada na fila de desconto'
        });
      }

    } catch (error) {
      console.error('Erro ao promover mensagem:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao promover mensagem'
      });
    }
  }

  // Obter métricas de cache e economia
  static async getCacheMetrics(req: CachedRequest, res: Response): Promise<void> {
    try {
      // Obter informações de rate limiting se o usuário estiver presente
      let rateLimitInfo = null;
      if (req.user?.id) {
        const { rateLimitService } = await import('../services/rateLimitService');
        const rateLimitResult = await rateLimitService.checkRateLimit(req.user.id, req.user.role || 'consulta');
        rateLimitInfo = rateLimitResult.info;
      }

      const [cacheMetrics, queueStats, budgetStatus] = await Promise.all([
        cacheService.getCacheMetrics(),
        queueService.getQueueStats(),
        costMonitoringService.getBudgetStatus(),
      ]);

      res.json({
        success: true,
        rateLimits: rateLimitInfo, // Adicionar rate limits na resposta
        data: {
          cache: cacheMetrics,
          queue: queueStats,
          budget: budgetStatus,
          discountInfo: {
            isActive: isDiscountTime(),
            nextTime: getNextDiscountTime(),
            potentialSavings: 'Até 70% de economia com cache + desconto',
          },
          realTimeCost: {
            perMessage: isDiscountTime() ? '$0.001095' : '$0.00219',
            dailySpent: `$${budgetStatus.daily.spent.toFixed(4)}`,
            dailyBudget: `$${budgetStatus.daily.budget.toFixed(2)}`,
            monthlyEstimate: `$${budgetStatus.monthly.spent.toFixed(2)}`,
          }
        }
      });

    } catch (error) {
      console.error('Erro ao obter métricas:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao consultar métricas'
      });
    }
  }

  // Obter relatório detalhado de custos
  static async getCostReport(req: CachedRequest, res: Response): Promise<void> {
    try {
      const { period = 'daily', date } = req.query;
      
      if (period !== 'daily' && period !== 'monthly') {
        res.status(400).json({
          success: false,
          error: 'Período deve ser "daily" ou "monthly"'
        });
        return;
      }

      const report = await costMonitoringService.getCostReport(
        period as 'daily' | 'monthly', 
        date as string
      );

      res.json({
        success: true,
        data: report
      });

    } catch (error) {
      console.error('Erro ao gerar relatório de custos:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno ao gerar relatório'
      });
    }
  }
}

export default ChatController;