{"mcpServers": {"context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "*****************************************/prefeituravirtual"]}}}